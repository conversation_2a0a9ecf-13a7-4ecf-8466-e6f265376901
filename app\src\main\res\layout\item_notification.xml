<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 알림 헤더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:id="@+id/iconNotification"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_notifications"
                app:tint="@color/purple_500"
                android:layout_marginEnd="12dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textNotificationTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📅 일정 초대"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/textNotificationTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="방금 전"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 알림 메시지 -->
        <TextView
            android:id="@+id/textNotificationMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="친구님이 일정에 초대했습니다"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="12dp"
            android:lineSpacingExtra="2dp" />

        <!-- 액션 버튼들 -->
        <LinearLayout
            android:id="@+id/layoutActions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnReject"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:layout_marginEnd="8dp"
                android:text="거절"
                android:textSize="14sp"
                android:textStyle="bold"
                app:cornerRadius="18dp"
                style="@style/Widget.Material3.Button.TextButton"
                android:textColor="@color/text_secondary"
                android:minWidth="80dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnAccept"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="수락"
                android:textSize="14sp"
                android:textStyle="bold"
                app:cornerRadius="18dp"
                app:backgroundTint="@color/ios_blue"
                android:textColor="@android:color/white"
                android:minWidth="80dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
