<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 일정 제목 -->
    <TextView
        android:id="@+id/textScheduleTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="회의 참석"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="12dp" />

    <!-- 시간 정보 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="12dp">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_time"
            app:tint="@color/ios_blue"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/textScheduleTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="오전 10:00"
            android:textSize="16sp"
            android:textColor="@color/text_primary" />

    </LinearLayout>

    <!-- 위치 정보 -->
    <LinearLayout
        android:id="@+id/layoutLocation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="16dp"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_location_start"
                app:tint="@color/ios_green"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/textDeparture"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="출발지"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_location_end"
                app:tint="@color/ios_red"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/textDestination"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="도착지"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

    <!-- 친구 정보 -->
    <LinearLayout
        android:id="@+id/layoutFriends"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_person"
            app:tint="@color/ios_orange"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/textFriends"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="함께하는 친구들"
            android:textSize="14sp"
            android:textColor="@color/text_secondary" />

    </LinearLayout>

    <!-- 메모 -->
    <LinearLayout
        android:id="@+id/layoutMemo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="20dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="메모"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/textMemo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="일정 메모 내용"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:background="@color/background_secondary"
            android:padding="12dp"
            android:minHeight="60dp" />

    </LinearLayout>

    <!-- 액션 버튼들 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="20dp">

        <Button
            android:id="@+id/btnEditSchedule"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="수정"
            android:layout_marginEnd="8dp"
            style="@style/Widget.Material3.Button.OutlinedButton" />

        <Button
            android:id="@+id/btnDeleteSchedule"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="삭제"
            android:layout_marginStart="8dp"
            android:backgroundTint="@color/ios_red" />

    </LinearLayout>

</LinearLayout>
