-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:2:1-69:12
INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:2:1-69:12
MERGED from [com.kakao.sdk:v2-user:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dd98040cf34efb4c647cfb0b9c1ac40\transformed\v2-user-2.19.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:2:1-22:12
MERGED from [com.kakao.sdk:v2-network:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1a1fdaa30248290ef3425e8fdb9d3ec\transformed\v2-network-2.19.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d862bf2b5a42c7d4dba2051d7d69072\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:2:1-24:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3104310070aa521e0a811bb63bfe0177\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\560962a121f6ed5b86b69c6ee9329d69\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed874117c9c65f406b3b8e55fc39afa3\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4d96d74a14046b91d40cca3b0165b0\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f77804f9fc0a0391ec02c7b7fef452cc\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\844f5d9328c7c028844d43f11e1bb1f5\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17a30490300b033b1772538f2657fe99\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4c630fd0951313ea28adde522c87ae\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\630c6c3476789001e5d078c8dc912e22\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\378a96686f3cfcbfd3bdcb204b4c78ca\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd1a4a0cbd3bb42f6893ae81c437e2b1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35b2e474d3332543d0ae0366c07dbfd0\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c4edc17630951cf74586009cd1dd1c1\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\712a6bbecbfb44cfdd41df523efdfe30\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\265fe3f3360174eccaaaa0945467fbbe\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09d9eb7809036a872ff86964716af4a2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03aec2c1b2d4b822f2f6d93162037d6b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4741cf45e1eee184090d5422ee18388\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36d11ddaf7526eb0ddf91602b42d8070\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda92deac3742e21d61083e50a8ea4bf\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a59370747b5888872782a6dae5f375a9\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcccced35f654e81232cf08c4afbe3a0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a7def5263d752e4a413b1c401624d1e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be16d1ed538961bc40cd37521047a687\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54b01a3f0970497cbaf098d4f59f5787\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85db21db2d396b7cfb1ef0739871e833\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\264f759feaf17916cff6670827a98e87\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02344620b027cd4b51022a2f63eb19e2\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aa451b2c9b6d3bd6b8a8a0c5eab5d86\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b75e4effa51b703bfc1a44c215e615\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944a1d5b75c6891388494ecec0f7ee13\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fea6008cb61f543f566533a44f46cf44\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\214db8367d7230bfd2cea26184b1c723\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80312748ffdfa6d6d373115471d94e83\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b273577ec1a001e95b5541801b37991\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d75dade586afbe1d1c221fdf6849f78\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fd40464b01d87ffadc105807dbd2d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\357bdad68ffbea2e177712891b98fcb4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9749df20cc7456761364a622721a6\transformed\sqlite-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ada30ec73165fa5df5a5a5b588341cde\transformed\sqlite-framework-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f85a1d82247af7b8f1808ccf51bb896\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8176e8bd1f59c0efc187341e0668f6f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf953b7c460467626db42bbdf1dfcb89\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:5-77
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:22-74
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:5-66
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:22-63
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:22-78
application
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:14:5-67:19
INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:14:5-67:19
MERGED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:9:5-20:19
MERGED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:9:5-20:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3104310070aa521e0a811bb63bfe0177\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3104310070aa521e0a811bb63bfe0177\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\560962a121f6ed5b86b69c6ee9329d69\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\560962a121f6ed5b86b69c6ee9329d69\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\357bdad68ffbea2e177712891b98fcb4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\357bdad68ffbea2e177712891b98fcb4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:22:9-35
	android:label
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:20:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:18:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:21:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:25:9-29
	android:icon
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:19:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:16:9-35
	android:theme
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:23:9-45
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:17:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:24:9-44
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:15:9-44
activity#com.example.timemate.MainActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:30:9-37:20
	android:exported
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:32:13-36
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:31:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:33:13-36:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:17-69
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:17-77
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:27-74
activity#com.example.timemate.SignupFormActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:39:9-56
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:39:19-53
activity#com.example.timemate.ManualLoginActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:40:9-57
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:40:19-54
activity#com.example.timemate.PasswordResetActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:9-59
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:19-56
activity#com.example.timemate.features.notification.NotificationActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:9-80
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:19-77
activity#com.example.timemate.ScheduleReminderDetailActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:9-68
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:19-65
activity#com.example.timemate.AccountSwitchActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:44:9-59
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:44:19-56
activity#com.example.timemate.features.home.HomeActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:9-64
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:19-61
activity#com.example.timemate.features.schedule.ScheduleAddActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:9-75
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:19-72
activity#com.example.timemate.features.schedule.ScheduleListActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:9-76
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:19-73
activity#com.example.timemate.features.friend.FriendListActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:9-72
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:19-69
activity#com.example.timemate.features.friend.FriendAddActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:51:9-71
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:51:19-68
activity#com.example.timemate.features.profile.ProfileActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:52:9-70
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:52:19-67
activity#com.example.timemate.ui.home.HomeActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:9-58
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:19-55
activity#com.example.timemate.ui.schedule.ScheduleListActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:9-70
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:19-67
activity#com.example.timemate.ui.friend.FriendListActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:9-66
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:19-63
activity#com.example.timemate.ui.profile.ProfileActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:9-64
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:19-61
activity#com.example.timemate.ui.recommendation.RecommendationActivity
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:59:9-78
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:59:19-75
receiver#com.example.timemate.NotificationActionReceiver
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:9-62:40
	android:exported
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:62:13-37
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:19-61
receiver#com.example.timemate.notification.SnoozeReceiver
ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:9-66:40
	android:exported
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:66:13-37
	android:name
		ADDED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:19-62
uses-sdk
INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
MERGED from [com.kakao.sdk:v2-user:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dd98040cf34efb4c647cfb0b9c1ac40\transformed\v2-user-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.kakao.sdk:v2-user:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3dd98040cf34efb4c647cfb0b9c1ac40\transformed\v2-user-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.kakao.sdk:v2-network:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1a1fdaa30248290ef3425e8fdb9d3ec\transformed\v2-network-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.kakao.sdk:v2-network:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1a1fdaa30248290ef3425e8fdb9d3ec\transformed\v2-network-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d862bf2b5a42c7d4dba2051d7d69072\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6d862bf2b5a42c7d4dba2051d7d69072\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3104310070aa521e0a811bb63bfe0177\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3104310070aa521e0a811bb63bfe0177\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\560962a121f6ed5b86b69c6ee9329d69\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\560962a121f6ed5b86b69c6ee9329d69\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed874117c9c65f406b3b8e55fc39afa3\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ed874117c9c65f406b3b8e55fc39afa3\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4d96d74a14046b91d40cca3b0165b0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4d96d74a14046b91d40cca3b0165b0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f77804f9fc0a0391ec02c7b7fef452cc\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f77804f9fc0a0391ec02c7b7fef452cc\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\844f5d9328c7c028844d43f11e1bb1f5\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\844f5d9328c7c028844d43f11e1bb1f5\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17a30490300b033b1772538f2657fe99\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17a30490300b033b1772538f2657fe99\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4c630fd0951313ea28adde522c87ae\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2d4c630fd0951313ea28adde522c87ae\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\630c6c3476789001e5d078c8dc912e22\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\630c6c3476789001e5d078c8dc912e22\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\378a96686f3cfcbfd3bdcb204b4c78ca\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\378a96686f3cfcbfd3bdcb204b4c78ca\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd1a4a0cbd3bb42f6893ae81c437e2b1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dd1a4a0cbd3bb42f6893ae81c437e2b1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35b2e474d3332543d0ae0366c07dbfd0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\35b2e474d3332543d0ae0366c07dbfd0\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c4edc17630951cf74586009cd1dd1c1\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2c4edc17630951cf74586009cd1dd1c1\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\712a6bbecbfb44cfdd41df523efdfe30\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\712a6bbecbfb44cfdd41df523efdfe30\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\265fe3f3360174eccaaaa0945467fbbe\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\265fe3f3360174eccaaaa0945467fbbe\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09d9eb7809036a872ff86964716af4a2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09d9eb7809036a872ff86964716af4a2\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03aec2c1b2d4b822f2f6d93162037d6b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\03aec2c1b2d4b822f2f6d93162037d6b\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4741cf45e1eee184090d5422ee18388\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4741cf45e1eee184090d5422ee18388\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36d11ddaf7526eb0ddf91602b42d8070\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36d11ddaf7526eb0ddf91602b42d8070\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda92deac3742e21d61083e50a8ea4bf\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda92deac3742e21d61083e50a8ea4bf\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a59370747b5888872782a6dae5f375a9\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a59370747b5888872782a6dae5f375a9\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcccced35f654e81232cf08c4afbe3a0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dcccced35f654e81232cf08c4afbe3a0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a7def5263d752e4a413b1c401624d1e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3a7def5263d752e4a413b1c401624d1e\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be16d1ed538961bc40cd37521047a687\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\be16d1ed538961bc40cd37521047a687\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54b01a3f0970497cbaf098d4f59f5787\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54b01a3f0970497cbaf098d4f59f5787\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85db21db2d396b7cfb1ef0739871e833\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\85db21db2d396b7cfb1ef0739871e833\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\264f759feaf17916cff6670827a98e87\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\264f759feaf17916cff6670827a98e87\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02344620b027cd4b51022a2f63eb19e2\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\02344620b027cd4b51022a2f63eb19e2\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aa451b2c9b6d3bd6b8a8a0c5eab5d86\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7aa451b2c9b6d3bd6b8a8a0c5eab5d86\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b75e4effa51b703bfc1a44c215e615\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b75e4effa51b703bfc1a44c215e615\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944a1d5b75c6891388494ecec0f7ee13\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\944a1d5b75c6891388494ecec0f7ee13\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fea6008cb61f543f566533a44f46cf44\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fea6008cb61f543f566533a44f46cf44\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\214db8367d7230bfd2cea26184b1c723\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\214db8367d7230bfd2cea26184b1c723\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80312748ffdfa6d6d373115471d94e83\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\80312748ffdfa6d6d373115471d94e83\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b273577ec1a001e95b5541801b37991\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b273577ec1a001e95b5541801b37991\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d75dade586afbe1d1c221fdf6849f78\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d75dade586afbe1d1c221fdf6849f78\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fd40464b01d87ffadc105807dbd2d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fd40464b01d87ffadc105807dbd2d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\357bdad68ffbea2e177712891b98fcb4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\357bdad68ffbea2e177712891b98fcb4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9749df20cc7456761364a622721a6\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00e9749df20cc7456761364a622721a6\transformed\sqlite-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ada30ec73165fa5df5a5a5b588341cde\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework-android:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ada30ec73165fa5df5a5a5b588341cde\transformed\sqlite-framework-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f85a1d82247af7b8f1808ccf51bb896\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1f85a1d82247af7b8f1808ccf51bb896\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8176e8bd1f59c0efc187341e0668f6f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8176e8bd1f59c0efc187341e0668f6f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf953b7c460467626db42bbdf1dfcb89\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf953b7c460467626db42bbdf1dfcb89\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml
activity#com.kakao.sdk.auth.TalkAuthCodeActivity
ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:10:9-15:56
	android:launchMode
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:14:13-44
	android:exported
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:13:13-37
	android:configChanges
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:12:13-74
	android:theme
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:15:13-53
	android:name
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:11:13-67
activity#com.kakao.sdk.auth.AuthCodeHandlerActivity
ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:16:9-19:56
	android:launchMode
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:18:13-44
	android:theme
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:19:13-53
	android:name
		ADDED from [com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:17:13-70
queries
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:9:5-22:15
package#com.kakao.talk
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:9-50
	android:name
		ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:18-47
package#com.kakao.talk.alpha
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:9-56
	android:name
		ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:18-53
package#com.kakao.talk.sandbox
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:9-58
	android:name
		ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:18-55
package#com.kakao.onetalk
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:9-53
	android:name
		ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:18-50
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:15:9-21:18
action#android.intent.action.VIEW
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:13-65
	android:name
		ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:13-74
	android:name
		ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:23-71
data
ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:13-44
	android:scheme
		ADDED from [com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:19-41
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6ac2588e2144b2e16f39927e0dc770d\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
