1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.timemate"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="27"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:5-66
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:22-63
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:5-68
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:5-81
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:22-78
19
20    <queries>
20-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:9:5-22:15
21        <package android:name="com.kakao.talk" />
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:9-50
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:18-47
22        <package android:name="com.kakao.talk.alpha" />
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:9-56
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:18-53
23        <package android:name="com.kakao.talk.sandbox" />
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:9-58
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:18-55
24        <package android:name="com.kakao.onetalk" />
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:9-53
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:18-50
25
26        <intent>
26-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:15:9-21:18
27            <action android:name="android.intent.action.VIEW" />
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:13-65
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:21-62
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:13-74
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:23-71
30
31            <data android:scheme="https" />
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:13-44
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:19-41
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
36
37    <permission
37-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:14:5-67:19
44        android:name="com.example.timemate.TimeMateApplication"
44-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:15:9-44
45        android:allowBackup="true"
45-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:16:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:17:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:18:9-54
51        android:icon="@mipmap/app_icon"
51-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:19:9-40
52        android:label="@string/app_name"
52-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:20:9-41
53        android:roundIcon="@mipmap/app_icon"
53-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:21:9-45
54        android:supportsRtl="true"
54-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:22:9-35
55        android:theme="@style/TimeMateTheme"
55-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:23:9-45
56        android:usesCleartextTraffic="true" >
56-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:24:9-44
57
58        <!-- API 키는 BuildConfig를 통해 안전하게 관리됩니다 -->
59        <!-- 메타데이터 방식은 보안상 위험하므로 제거하고 BuildConfig 사용 -->
60
61        <activity
61-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:30:9-37:20
62            android:name="com.example.timemate.MainActivity"
62-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:31:13-41
63            android:exported="true" >
63-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:32:13-36
64            <intent-filter>
64-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:33:13-36:29
65                <action android:name="android.intent.action.MAIN" />
65-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:17-69
65-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:25-66
66
67                <category android:name="android.intent.category.LAUNCHER" />
67-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:17-77
67-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:27-74
68            </intent-filter>
69        </activity>
70        <activity android:name="com.example.timemate.SignupFormActivity" />
70-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:39:9-56
70-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:39:19-53
71        <activity android:name="com.example.timemate.ManualLoginActivity" />
71-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:40:9-57
71-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:40:19-54
72        <activity android:name="com.example.timemate.PasswordResetActivity" />
72-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:9-59
72-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:19-56
73        <activity android:name="com.example.timemate.features.notification.NotificationActivity" />
73-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:9-80
73-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:19-77
74        <activity android:name="com.example.timemate.ScheduleReminderDetailActivity" />
74-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:9-68
74-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:19-65
75        <activity android:name="com.example.timemate.AccountSwitchActivity" />
75-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:44:9-59
75-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:44:19-56
76
77        <!-- 새로운 features 패키지 구조의 Activity들 -->
78        <activity android:name="com.example.timemate.features.home.HomeActivity" />
78-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:9-64
78-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:19-61
79        <activity android:name="com.example.timemate.features.schedule.ScheduleAddActivity" />
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:9-75
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:19-72
80        <activity android:name="com.example.timemate.features.schedule.ScheduleListActivity" />
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:9-76
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:19-73
81        <activity android:name="com.example.timemate.features.friend.FriendListActivity" />
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:9-72
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:19-69
82        <activity android:name="com.example.timemate.features.friend.FriendAddActivity" />
82-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:51:9-71
82-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:51:19-68
83        <activity android:name="com.example.timemate.features.profile.ProfileActivity" />
83-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:52:9-70
83-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:52:19-67
84
85        <!-- 기존 ui 패키지 Activity들도 유지 (호환성) -->
86        <activity android:name="com.example.timemate.ui.home.HomeActivity" />
86-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:9-58
86-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:19-55
87        <activity android:name="com.example.timemate.ui.schedule.ScheduleListActivity" />
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:9-70
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:19-67
88        <activity android:name="com.example.timemate.ui.friend.FriendListActivity" />
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:9-66
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:19-63
89        <activity android:name="com.example.timemate.ui.profile.ProfileActivity" />
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:9-64
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:19-61
90        <activity android:name="com.example.timemate.ui.recommendation.RecommendationActivity" />
90-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:59:9-78
90-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:59:19-75
91
92        <receiver
92-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:9-62:40
93            android:name="com.example.timemate.NotificationActionReceiver"
93-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:19-61
94            android:exported="false" />
94-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:62:13-37
95
96        <!-- 일정 알림 Snooze 기능 -->
97        <receiver
97-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:9-66:40
98            android:name="com.example.timemate.notification.SnoozeReceiver"
98-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:19-62
99            android:exported="false" />
99-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:66:13-37
100
101        <activity
101-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:10:9-15:56
102            android:name="com.kakao.sdk.auth.TalkAuthCodeActivity"
102-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:11:13-67
103            android:configChanges="orientation|screenSize|keyboardHidden"
103-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:12:13-74
104            android:exported="false"
104-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:13:13-37
105            android:launchMode="singleTask"
105-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:14:13-44
106            android:theme="@style/TransparentCompat" />
106-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:15:13-53
107        <activity
107-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:16:9-19:56
108            android:name="com.kakao.sdk.auth.AuthCodeHandlerActivity"
108-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:17:13-70
109            android:launchMode="singleTask"
109-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:18:13-44
110            android:theme="@style/TransparentCompat" />
110-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:19:13-53
111
112        <provider
112-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
113            android:name="androidx.startup.InitializationProvider"
113-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
114            android:authorities="com.example.timemate.androidx-startup"
114-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
115            android:exported="false" >
115-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
116            <meta-data
116-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
117                android:name="androidx.work.WorkManagerInitializer"
117-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
118                android:value="androidx.startup" />
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
119            <meta-data
119-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
120                android:name="androidx.emoji2.text.EmojiCompatInitializer"
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
121                android:value="androidx.startup" />
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
122            <meta-data
122-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
123                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
123-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
124                android:value="androidx.startup" />
124-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
125            <meta-data
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
127                android:value="androidx.startup" />
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
128        </provider>
129
130        <service
130-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
131            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
132            android:directBootAware="false"
132-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
133            android:enabled="@bool/enable_system_alarm_service_default"
133-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
134            android:exported="false" />
134-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
135        <service
135-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
136            android:name="androidx.work.impl.background.systemjob.SystemJobService"
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
137            android:directBootAware="false"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
138            android:enabled="@bool/enable_system_job_service_default"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
139            android:exported="true"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
140            android:permission="android.permission.BIND_JOB_SERVICE" />
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
141        <service
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
142            android:name="androidx.work.impl.foreground.SystemForegroundService"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
144            android:enabled="@bool/enable_system_foreground_service_default"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
145            android:exported="false" />
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
146
147        <receiver
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
148            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
150            android:enabled="true"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
151            android:exported="false" />
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
152        <receiver
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
153            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
155            android:enabled="false"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
156            android:exported="false" >
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
157            <intent-filter>
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
158                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
159                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
160            </intent-filter>
161        </receiver>
162        <receiver
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
163            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
164            android:directBootAware="false"
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
165            android:enabled="false"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
166            android:exported="false" >
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
167            <intent-filter>
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
168                <action android:name="android.intent.action.BATTERY_OKAY" />
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
169                <action android:name="android.intent.action.BATTERY_LOW" />
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
173            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
175            android:enabled="false"
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
178                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
179                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
180            </intent-filter>
181        </receiver>
182        <receiver
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
183            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
185            android:enabled="false"
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
186            android:exported="false" >
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
187            <intent-filter>
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
188                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
189            </intent-filter>
190        </receiver>
191        <receiver
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
192            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
193            android:directBootAware="false"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
194            android:enabled="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
195            android:exported="false" >
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
196            <intent-filter>
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
197                <action android:name="android.intent.action.BOOT_COMPLETED" />
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
198                <action android:name="android.intent.action.TIME_SET" />
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
199                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
200            </intent-filter>
201        </receiver>
202        <receiver
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
203            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
205            android:enabled="@bool/enable_system_alarm_service_default"
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
207            <intent-filter>
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
208                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
209            </intent-filter>
210        </receiver>
211        <receiver
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
212            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
213            android:directBootAware="false"
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
214            android:enabled="true"
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
215            android:exported="true"
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
216            android:permission="android.permission.DUMP" >
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
217            <intent-filter>
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
218                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
219            </intent-filter>
220        </receiver>
221
222        <service
222-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
223            android:name="androidx.room.MultiInstanceInvalidationService"
223-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
224            android:directBootAware="true"
224-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
225            android:exported="false" />
225-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
226
227        <receiver
227-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
228            android:name="androidx.profileinstaller.ProfileInstallReceiver"
228-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
229            android:directBootAware="false"
229-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
230            android:enabled="true"
230-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
231            android:exported="true"
231-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
232            android:permission="android.permission.DUMP" >
232-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
233            <intent-filter>
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
234                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
235            </intent-filter>
236            <intent-filter>
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
237                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
238            </intent-filter>
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
240                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
243                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
244            </intent-filter>
245        </receiver>
246    </application>
247
248</manifest>
