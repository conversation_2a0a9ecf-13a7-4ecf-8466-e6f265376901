1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.timemate"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="27"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:5-66
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:22-63
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:5-68
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:5-81
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:22-78
19
20    <queries>
20-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:9:5-22:15
21        <package android:name="com.kakao.talk" />
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:9-50
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:18-47
22        <package android:name="com.kakao.talk.alpha" />
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:9-56
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:18-53
23        <package android:name="com.kakao.talk.sandbox" />
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:9-58
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:18-55
24        <package android:name="com.kakao.onetalk" />
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:9-53
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:18-50
25
26        <intent>
26-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:15:9-21:18
27            <action android:name="android.intent.action.VIEW" />
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:13-65
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:21-62
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:13-74
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:23-71
30
31            <data android:scheme="https" />
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:13-44
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:19-41
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
36
37    <permission
37-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:14:5-73:19
44        android:name="com.example.timemate.TimeMateApplication"
44-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:15:9-44
45        android:allowBackup="true"
45-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:16:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:17:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:18:9-54
51        android:icon="@mipmap/app_icon"
51-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:19:9-40
52        android:label="@string/app_name"
52-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:20:9-41
53        android:roundIcon="@mipmap/app_icon"
53-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:21:9-45
54        android:supportsRtl="true"
54-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:22:9-35
55        android:testOnly="true"
56        android:theme="@style/TimeMateTheme"
56-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:23:9-45
57        android:usesCleartextTraffic="true" >
57-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:24:9-44
58
59        <!-- API 키는 BuildConfig를 통해 안전하게 관리됩니다 -->
60        <!-- 메타데이터 방식은 보안상 위험하므로 제거하고 BuildConfig 사용 -->
61
62
63        <!-- 스플래시 화면 (메인 런처) -->
64        <activity
64-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:31:9-39:20
65            android:name="com.example.timemate.SplashActivity"
65-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:32:13-43
66            android:exported="true"
66-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:33:13-36
67            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" >
67-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:13-71
68            <intent-filter>
68-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:13-38:29
69                <action android:name="android.intent.action.MAIN" />
69-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:36:17-69
69-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:36:25-66
70
71                <category android:name="android.intent.category.LAUNCHER" />
71-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:37:17-77
71-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:37:27-74
72            </intent-filter>
73        </activity>
74        <activity
74-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:9-43:40
75            android:name="com.example.timemate.MainActivity"
75-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:13-41
76            android:exported="false" />
76-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:13-37
77        <activity android:name="com.example.timemate.SignupFormActivity" />
77-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:45:9-56
77-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:45:19-53
78        <activity android:name="com.example.timemate.ManualLoginActivity" />
78-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:46:9-57
78-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:46:19-54
79        <activity android:name="com.example.timemate.PasswordResetActivity" />
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:9-59
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:19-56
80        <activity android:name="com.example.timemate.features.notification.NotificationActivity" />
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:9-80
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:19-77
81        <activity android:name="com.example.timemate.ScheduleReminderDetailActivity" />
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:9-68
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:19-65
82        <activity android:name="com.example.timemate.AccountSwitchActivity" />
82-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:9-59
82-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:19-56
83
84        <!-- 새로운 features 패키지 구조의 Activity들 -->
85        <activity android:name="com.example.timemate.features.home.HomeActivity" />
85-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:53:9-64
85-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:53:19-61
86        <activity android:name="com.example.timemate.features.schedule.ScheduleAddActivity" />
86-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:54:9-75
86-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:54:19-72
87        <activity android:name="com.example.timemate.features.schedule.ScheduleListActivity" />
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:9-76
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:19-73
88        <activity android:name="com.example.timemate.features.friend.FriendListActivity" />
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:9-72
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:19-69
89        <activity android:name="com.example.timemate.features.friend.FriendAddActivity" />
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:9-71
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:19-68
90        <activity android:name="com.example.timemate.features.profile.ProfileActivity" />
90-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:9-70
90-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:19-67
91
92        <!-- 기존 ui 패키지 Activity들도 유지 (호환성) -->
93        <activity android:name="com.example.timemate.ui.home.HomeActivity" />
93-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:9-58
93-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:19-55
94        <activity android:name="com.example.timemate.ui.schedule.ScheduleListActivity" />
94-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:62:9-70
94-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:62:19-67
95        <activity android:name="com.example.timemate.ui.friend.FriendListActivity" />
95-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:63:9-66
95-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:63:19-63
96        <activity android:name="com.example.timemate.ui.profile.ProfileActivity" />
96-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:64:9-64
96-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:64:19-61
97        <activity android:name="com.example.timemate.ui.recommendation.RecommendationActivity" />
97-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:9-78
97-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:19-75
98
99        <receiver
99-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:67:9-68:40
100            android:name="com.example.timemate.NotificationActionReceiver"
100-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:67:19-61
101            android:exported="false" />
101-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:68:13-37
102
103        <!-- 일정 알림 Snooze 기능 -->
104        <receiver
104-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:71:9-72:40
105            android:name="com.example.timemate.notification.SnoozeReceiver"
105-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:71:19-62
106            android:exported="false" />
106-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:72:13-37
107
108        <activity
108-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:10:9-15:56
109            android:name="com.kakao.sdk.auth.TalkAuthCodeActivity"
109-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:11:13-67
110            android:configChanges="orientation|screenSize|keyboardHidden"
110-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:12:13-74
111            android:exported="false"
111-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:13:13-37
112            android:launchMode="singleTask"
112-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:14:13-44
113            android:theme="@style/TransparentCompat" />
113-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:15:13-53
114        <activity
114-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:16:9-19:56
115            android:name="com.kakao.sdk.auth.AuthCodeHandlerActivity"
115-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:17:13-70
116            android:launchMode="singleTask"
116-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:18:13-44
117            android:theme="@style/TransparentCompat" />
117-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:19:13-53
118
119        <provider
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
120            android:name="androidx.startup.InitializationProvider"
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
121            android:authorities="com.example.timemate.androidx-startup"
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
122            android:exported="false" >
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
123            <meta-data
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
124                android:name="androidx.work.WorkManagerInitializer"
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
125                android:value="androidx.startup" />
125-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
126            <meta-data
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.emoji2.text.EmojiCompatInitializer"
127-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
128                android:value="androidx.startup" />
128-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
130-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
131                android:value="androidx.startup" />
131-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
134                android:value="androidx.startup" />
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
135        </provider>
136
137        <service
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
138            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
140            android:enabled="@bool/enable_system_alarm_service_default"
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
141            android:exported="false" />
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
142        <service
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
143            android:name="androidx.work.impl.background.systemjob.SystemJobService"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
144            android:directBootAware="false"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
145            android:enabled="@bool/enable_system_job_service_default"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
146            android:exported="true"
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
147            android:permission="android.permission.BIND_JOB_SERVICE" />
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
148        <service
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
149            android:name="androidx.work.impl.foreground.SystemForegroundService"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
151            android:enabled="@bool/enable_system_foreground_service_default"
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
152            android:exported="false" />
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
153
154        <receiver
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
155            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
157            android:enabled="true"
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
158            android:exported="false" />
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
159        <receiver
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
160            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
161            android:directBootAware="false"
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
162            android:enabled="false"
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
163            android:exported="false" >
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
164            <intent-filter>
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
165                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
166                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
167            </intent-filter>
168        </receiver>
169        <receiver
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
170            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
172            android:enabled="false"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
173            android:exported="false" >
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
174            <intent-filter>
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
175                <action android:name="android.intent.action.BATTERY_OKAY" />
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
176                <action android:name="android.intent.action.BATTERY_LOW" />
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
177            </intent-filter>
178        </receiver>
179        <receiver
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
180            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
181            android:directBootAware="false"
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
182            android:enabled="false"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
183            android:exported="false" >
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
184            <intent-filter>
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
185                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
186                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
187            </intent-filter>
188        </receiver>
189        <receiver
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
190            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
191            android:directBootAware="false"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
192            android:enabled="false"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
193            android:exported="false" >
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
194            <intent-filter>
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
195                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
196            </intent-filter>
197        </receiver>
198        <receiver
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
199            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
201            android:enabled="false"
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
203            <intent-filter>
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
204                <action android:name="android.intent.action.BOOT_COMPLETED" />
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
205                <action android:name="android.intent.action.TIME_SET" />
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
206                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
207            </intent-filter>
208        </receiver>
209        <receiver
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
210            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
211            android:directBootAware="false"
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
212            android:enabled="@bool/enable_system_alarm_service_default"
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
213            android:exported="false" >
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
214            <intent-filter>
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
215                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
216            </intent-filter>
217        </receiver>
218        <receiver
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
219            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
221            android:enabled="true"
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
222            android:exported="true"
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
223            android:permission="android.permission.DUMP" >
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
224            <intent-filter>
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
225                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
226            </intent-filter>
227        </receiver>
228
229        <service
229-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
230            android:name="androidx.room.MultiInstanceInvalidationService"
230-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
231            android:directBootAware="true"
231-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
232            android:exported="false" />
232-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
233
234        <receiver
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
235            android:name="androidx.profileinstaller.ProfileInstallReceiver"
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
236            android:directBootAware="false"
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
237            android:enabled="true"
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
238            android:exported="true"
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
239            android:permission="android.permission.DUMP" >
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
240            <intent-filter>
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
241                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
241-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
242            </intent-filter>
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
244                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
247                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
250                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
251            </intent-filter>
252        </receiver>
253    </application>
254
255</manifest>
