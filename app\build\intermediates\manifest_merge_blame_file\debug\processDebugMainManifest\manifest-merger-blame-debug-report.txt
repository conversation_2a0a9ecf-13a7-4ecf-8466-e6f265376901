1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.timemate"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="27"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:5-66
16-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:10:22-63
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:5-68
17-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:5-81
18-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:12:22-78
19
20    <queries>
20-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:9:5-22:15
21        <package android:name="com.kakao.talk" />
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:9-50
21-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:10:18-47
22        <package android:name="com.kakao.talk.alpha" />
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:9-56
22-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:11:18-53
23        <package android:name="com.kakao.talk.sandbox" />
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:9-58
23-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:12:18-55
24        <package android:name="com.kakao.onetalk" />
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:9-53
24-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:13:18-50
25
26        <intent>
26-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:15:9-21:18
27            <action android:name="android.intent.action.VIEW" />
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:13-65
27-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:16:21-62
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:13-74
29-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:18:23-71
30
31            <data android:scheme="https" />
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:13-44
31-->[com.kakao.sdk:v2-common:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4abac19e221033d2f56e788af55d0ad7\transformed\v2-common-2.19.0\AndroidManifest.xml:20:19-41
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
35-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:22-74
36
37    <permission
37-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.timemate.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:14:5-73:19
44        android:name="com.example.timemate.TimeMateApplication"
44-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:15:9-44
45        android:allowBackup="true"
45-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:16:9-35
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d447aa9b1396eb1d1443234471551a7\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:17:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:18:9-54
51        android:icon="@mipmap/app_icon"
51-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:19:9-40
52        android:label="@string/app_name"
52-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:20:9-41
53        android:roundIcon="@mipmap/app_icon"
53-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:21:9-45
54        android:supportsRtl="true"
54-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:22:9-35
55        android:theme="@style/TimeMateTheme"
55-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:23:9-45
56        android:usesCleartextTraffic="true" >
56-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:24:9-44
57
58        <!-- API 키는 BuildConfig를 통해 안전하게 관리됩니다 -->
59        <!-- 메타데이터 방식은 보안상 위험하므로 제거하고 BuildConfig 사용 -->
60
61        <activity
61-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:30:9-37:20
62            android:name="com.example.timemate.MainActivity"
62-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:31:13-41
63            android:exported="true" >
63-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:32:13-36
64            <intent-filter>
64-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:33:13-36:29
65                <action android:name="android.intent.action.MAIN" />
65-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:17-69
65-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:34:25-66
66
67                <category android:name="android.intent.category.LAUNCHER" />
67-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:17-77
67-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:35:27-74
68            </intent-filter>
69        </activity>
70
71        <!-- 스플래시 화면 (나중에 활성화) -->
72        <activity
72-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:40:9-43:72
73            android:name="com.example.timemate.SplashActivity"
73-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:41:13-43
74            android:exported="false"
74-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:42:13-37
75            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
75-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:43:13-69
76        <activity android:name="com.example.timemate.SignupFormActivity" />
76-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:45:9-56
76-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:45:19-53
77        <activity android:name="com.example.timemate.ManualLoginActivity" />
77-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:46:9-57
77-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:46:19-54
78        <activity android:name="com.example.timemate.PasswordResetActivity" />
78-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:9-59
78-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:47:19-56
79        <activity android:name="com.example.timemate.features.notification.NotificationActivity" />
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:9-80
79-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:48:19-77
80        <activity android:name="com.example.timemate.ScheduleReminderDetailActivity" />
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:9-68
80-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:49:19-65
81        <activity android:name="com.example.timemate.AccountSwitchActivity" />
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:9-59
81-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:50:19-56
82
83        <!-- 새로운 features 패키지 구조의 Activity들 -->
84        <activity android:name="com.example.timemate.features.home.HomeActivity" />
84-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:53:9-64
84-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:53:19-61
85        <activity android:name="com.example.timemate.features.schedule.ScheduleAddActivity" />
85-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:54:9-75
85-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:54:19-72
86        <activity android:name="com.example.timemate.features.schedule.ScheduleListActivity" />
86-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:9-76
86-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55:19-73
87        <activity android:name="com.example.timemate.features.friend.FriendListActivity" />
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:9-72
87-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56:19-69
88        <activity android:name="com.example.timemate.features.friend.FriendAddActivity" />
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:9-71
88-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57:19-68
89        <activity android:name="com.example.timemate.features.profile.ProfileActivity" />
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:9-70
89-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58:19-67
90
91        <!-- 기존 ui 패키지 Activity들도 유지 (호환성) -->
92        <activity android:name="com.example.timemate.ui.home.HomeActivity" />
92-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:9-58
92-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:61:19-55
93        <activity android:name="com.example.timemate.ui.schedule.ScheduleListActivity" />
93-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:62:9-70
93-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:62:19-67
94        <activity android:name="com.example.timemate.ui.friend.FriendListActivity" />
94-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:63:9-66
94-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:63:19-63
95        <activity android:name="com.example.timemate.ui.profile.ProfileActivity" />
95-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:64:9-64
95-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:64:19-61
96        <activity android:name="com.example.timemate.ui.recommendation.RecommendationActivity" />
96-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:9-78
96-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:65:19-75
97
98        <receiver
98-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:67:9-68:40
99            android:name="com.example.timemate.NotificationActionReceiver"
99-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:67:19-61
100            android:exported="false" />
100-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:68:13-37
101
102        <!-- 일정 알림 Snooze 기능 -->
103        <receiver
103-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:71:9-72:40
104            android:name="com.example.timemate.notification.SnoozeReceiver"
104-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:71:19-62
105            android:exported="false" />
105-->C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:72:13-37
106
107        <activity
107-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:10:9-15:56
108            android:name="com.kakao.sdk.auth.TalkAuthCodeActivity"
108-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:11:13-67
109            android:configChanges="orientation|screenSize|keyboardHidden"
109-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:12:13-74
110            android:exported="false"
110-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:13:13-37
111            android:launchMode="singleTask"
111-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:14:13-44
112            android:theme="@style/TransparentCompat" />
112-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:15:13-53
113        <activity
113-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:16:9-19:56
114            android:name="com.kakao.sdk.auth.AuthCodeHandlerActivity"
114-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:17:13-70
115            android:launchMode="singleTask"
115-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:18:13-44
116            android:theme="@style/TransparentCompat" />
116-->[com.kakao.sdk:v2-auth:2.19.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bbd566c85aabf11edc4400148206595f\transformed\v2-auth-2.19.0\AndroidManifest.xml:19:13-53
117
118        <provider
118-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
119            android:name="androidx.startup.InitializationProvider"
119-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
120            android:authorities="com.example.timemate.androidx-startup"
120-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
121            android:exported="false" >
121-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
122            <meta-data
122-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
123                android:name="androidx.work.WorkManagerInitializer"
123-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
124                android:value="androidx.startup" />
124-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
125            <meta-data
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.emoji2.text.EmojiCompatInitializer"
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
127                android:value="androidx.startup" />
127-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad3de56ae1ff33cc1a8195de95eedcf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
128            <meta-data
128-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
129-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
130                android:value="androidx.startup" />
130-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50caff7e137a7da8a23bfda87917c629\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
131            <meta-data
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
133                android:value="androidx.startup" />
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
134        </provider>
135
136        <service
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
137            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
138            android:directBootAware="false"
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
139            android:enabled="@bool/enable_system_alarm_service_default"
139-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
140            android:exported="false" />
140-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
141        <service
141-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
142            android:name="androidx.work.impl.background.systemjob.SystemJobService"
142-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
143            android:directBootAware="false"
143-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
144            android:enabled="@bool/enable_system_job_service_default"
144-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
145            android:exported="true"
145-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
146            android:permission="android.permission.BIND_JOB_SERVICE" />
146-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
147        <service
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
148            android:name="androidx.work.impl.foreground.SystemForegroundService"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
150            android:enabled="@bool/enable_system_foreground_service_default"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
151            android:exported="false" />
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
152
153        <receiver
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
154            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
156            android:enabled="true"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
157            android:exported="false" />
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
158        <receiver
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
159            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
161            android:enabled="false"
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
162            android:exported="false" >
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
163            <intent-filter>
163-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
164                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
165                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
166            </intent-filter>
167        </receiver>
168        <receiver
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
169            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
171            android:enabled="false"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
173            <intent-filter>
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
174                <action android:name="android.intent.action.BATTERY_OKAY" />
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
175                <action android:name="android.intent.action.BATTERY_LOW" />
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
176            </intent-filter>
177        </receiver>
178        <receiver
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
179            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
181            android:enabled="false"
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
183            <intent-filter>
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
184                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
185                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
191            android:enabled="false"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
194                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
198            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
200            android:enabled="false"
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
201            android:exported="false" >
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
202            <intent-filter>
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
203                <action android:name="android.intent.action.BOOT_COMPLETED" />
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
204                <action android:name="android.intent.action.TIME_SET" />
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
205                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
209            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
211            android:enabled="@bool/enable_system_alarm_service_default"
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
214                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
215            </intent-filter>
216        </receiver>
217        <receiver
217-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
218            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
218-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
220            android:enabled="true"
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
221            android:exported="true"
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
222            android:permission="android.permission.DUMP" >
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
223            <intent-filter>
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
224                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8990f0ac34fb7735236aa51857ad6a95\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
225            </intent-filter>
226        </receiver>
227
228        <service
228-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:24:9-28:63
229            android:name="androidx.room.MultiInstanceInvalidationService"
229-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:25:13-74
230            android:directBootAware="true"
230-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:26:13-43
231            android:exported="false" />
231-->[androidx.room:room-runtime-android:2.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8239c7620f2b266a50b8bcdfdddedd20\transformed\room-runtime-release\AndroidManifest.xml:27:13-37
232
233        <receiver
233-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
234            android:name="androidx.profileinstaller.ProfileInstallReceiver"
234-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
235            android:directBootAware="false"
235-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
236            android:enabled="true"
236-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
237            android:exported="true"
237-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
238            android:permission="android.permission.DUMP" >
238-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
239            <intent-filter>
239-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
240                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
240-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
241            </intent-filter>
242            <intent-filter>
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
243                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
244            </intent-filter>
245            <intent-filter>
245-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
246                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
246-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
247            </intent-filter>
248            <intent-filter>
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
249                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\50ee1b3e0e2a7f0acdc2d932f47a06a2\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
250            </intent-filter>
251        </receiver>
252    </application>
253
254</manifest>
