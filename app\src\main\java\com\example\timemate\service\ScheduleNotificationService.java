package com.example.timemate.service;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.util.Log;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.example.timemate.R;
import com.example.timemate.data.database.AppDatabase;
import com.example.timemate.data.model.Schedule;
import com.example.timemate.features.home.HomeActivity;
import com.example.timemate.util.UserSession;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 일정 알림 서비스
 * 오늘/내일 일정에 대한 알림을 관리합니다
 */
public class ScheduleNotificationService {

    private static final String TAG = "ScheduleNotificationService";
    private static final String CHANNEL_ID = "schedule_notifications";
    private static final String CHANNEL_NAME = "일정 알림";
    private static final String CHANNEL_DESCRIPTION = "오늘과 내일의 일정 알림";
    
    // 알림 ID
    private static final int NOTIFICATION_ID_TODAY = 1001;
    private static final int NOTIFICATION_ID_TOMORROW = 1002;
    
    private Context context;
    private AppDatabase database;
    private UserSession userSession;

    public ScheduleNotificationService(Context context) {
        this.context = context;
        this.database = AppDatabase.getInstance(context);
        this.userSession = UserSession.getInstance(context);
        
        createNotificationChannel();
    }

    /**
     * 알림 채널 생성 (Android 8.0 이상)
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableVibration(true);
            channel.setShowBadge(true);

            NotificationManager notificationManager = context.getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
                Log.d(TAG, "✅ 알림 채널 생성 완료");
            }
        }
    }

    /**
     * 오늘과 내일 일정 확인 후 알림 표시
     */
    public void checkAndShowScheduleNotifications() {
        new Thread(() -> {
            try {
                String currentUserId = userSession != null ? userSession.getCurrentUserId() : null;
                if (currentUserId == null) {
                    Log.w(TAG, "❌ 사용자 ID가 없어서 알림 확인 불가");
                    return;
                }

                Log.d(TAG, "🔔 일정 알림 확인 시작 - 사용자: " + currentUserId);

                // 오늘과 내일 날짜 계산
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
                String todayStr = dateFormat.format(new Date());
                
                Calendar tomorrow = Calendar.getInstance();
                tomorrow.add(Calendar.DAY_OF_MONTH, 1);
                String tomorrowStr = dateFormat.format(tomorrow.getTime());

                // 모든 일정 조회
                List<Schedule> allSchedules = database.scheduleDao().getSchedulesByUserId(currentUserId);
                
                // 오늘/내일 일정 분류
                List<Schedule> todaySchedules = new ArrayList<>();
                List<Schedule> tomorrowSchedules = new ArrayList<>();
                
                if (allSchedules != null) {
                    for (Schedule schedule : allSchedules) {
                        if (todayStr.equals(schedule.date)) {
                            todaySchedules.add(schedule);
                        } else if (tomorrowStr.equals(schedule.date)) {
                            tomorrowSchedules.add(schedule);
                        }
                    }
                }

                Log.d(TAG, "📅 오늘 일정: " + todaySchedules.size() + "개, 내일 일정: " + tomorrowSchedules.size() + "개");

                // 알림 표시
                if (!todaySchedules.isEmpty()) {
                    showTodayScheduleNotification(todaySchedules);
                }
                
                if (!tomorrowSchedules.isEmpty()) {
                    showTomorrowScheduleNotification(tomorrowSchedules);
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ 일정 알림 확인 중 오류", e);
            }
        }).start();
    }

    /**
     * 오늘 일정 알림 표시
     */
    private void showTodayScheduleNotification(List<Schedule> todaySchedules) {
        try {
            String title = "📅 오늘의 일정";
            String content;
            
            if (todaySchedules.size() == 1) {
                Schedule schedule = todaySchedules.get(0);
                content = schedule.time != null ? 
                    schedule.title + " (" + schedule.time + ")" : 
                    schedule.title;
            } else {
                content = "오늘 " + todaySchedules.size() + "개의 일정이 있습니다";
            }

            showNotification(NOTIFICATION_ID_TODAY, title, content, "오늘 일정을 확인하세요");
            Log.d(TAG, "✅ 오늘 일정 알림 표시: " + content);

        } catch (Exception e) {
            Log.e(TAG, "❌ 오늘 일정 알림 표시 오류", e);
        }
    }

    /**
     * 내일 일정 알림 표시
     */
    private void showTomorrowScheduleNotification(List<Schedule> tomorrowSchedules) {
        try {
            String title = "📋 내일의 일정";
            String content;
            
            if (tomorrowSchedules.size() == 1) {
                Schedule schedule = tomorrowSchedules.get(0);
                content = schedule.time != null ? 
                    schedule.title + " (" + schedule.time + ")" : 
                    schedule.title;
            } else {
                content = "내일 " + tomorrowSchedules.size() + "개의 일정이 있습니다";
            }

            showNotification(NOTIFICATION_ID_TOMORROW, title, content, "내일 일정을 미리 확인하세요");
            Log.d(TAG, "✅ 내일 일정 알림 표시: " + content);

        } catch (Exception e) {
            Log.e(TAG, "❌ 내일 일정 알림 표시 오류", e);
        }
    }

    /**
     * 실제 알림 표시
     */
    private void showNotification(int notificationId, String title, String content, String subText) {
        try {
            // 홈화면으로 이동하는 Intent
            Intent intent = new Intent(context, HomeActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            
            PendingIntent pendingIntent = PendingIntent.getActivity(
                context, 
                notificationId, 
                intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            // 알림 빌드
            NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.mipmap.app_icon)
                .setContentTitle(title)
                .setContentText(content)
                .setSubText(subText)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(content));

            // 알림 표시
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
            notificationManager.notify(notificationId, builder.build());

        } catch (Exception e) {
            Log.e(TAG, "❌ 알림 표시 오류", e);
        }
    }

    /**
     * 모든 일정 알림 제거
     */
    public void clearAllScheduleNotifications() {
        try {
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
            notificationManager.cancel(NOTIFICATION_ID_TODAY);
            notificationManager.cancel(NOTIFICATION_ID_TOMORROW);
            Log.d(TAG, "🗑️ 모든 일정 알림 제거 완료");
        } catch (Exception e) {
            Log.e(TAG, "❌ 알림 제거 오류", e);
        }
    }

    /**
     * 특정 알림 제거
     */
    public void clearNotification(int notificationId) {
        try {
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
            notificationManager.cancel(notificationId);
            Log.d(TAG, "🗑️ 알림 제거 완료: ID=" + notificationId);
        } catch (Exception e) {
            Log.e(TAG, "❌ 알림 제거 오류", e);
        }
    }
}
