http://schemas.android.com/apk/res-auto;androidx.constraintlayout:constraintlayout:2.2.1;$GRADLE_USER_HOME/caches/8.11.1/transforms/560962a121f6ed5b86b69c6ee9329d69/transformed/constraintlayout-2.2.1/res/values/values.xml,+attr:layout_constraintEnd_toEndOf,0,V4009919e4,b009b1a5a,;enum|reference:parent:0,;layout_constraintEnd_toEndOf,0,V801cf4bc1,3301cf4bec,;:;layout_constraintEnd_toEndOf,0,V80272662b,3302726656,;:;layout_constraintEnd_toEndOf,0,V8038190eb,3303819116,;:;layout_constraintEnd_toEndOf,0,V804aabf16,3304aabf41,;:;textPanY,0,V80508d047,2e0508d06d,;float:;textPanX,0,V80507d018,2e0507d03e,;float:;android\:maxWidth,0,V801f85372,2701f85391,;:;android\:maxWidth,0,V802275a4f,2702275a6e,;:;android\:maxWidth,0,V80317801f,270317803e,;:;android\:maxWidth,0,V803a9989b,2703a998ba,;:;customPixelDimension,0,V803dda05b,3e03dda091,;dimension:;android\:typeface,0,V80509d076,270509d095,;:;android\:typeface,0,V80587e566,270587e585,;:;android\:shadowDx,0,V80589e5b9,270589e5d8,;:;layout_constraintBaseline_toTopOf,0,V4008b171d,b008d1798,;enum|reference:parent:0,;layout_constraintBaseline_toTopOf,0,V801ca4aa8,3801ca4ad8,;:;layout_constraintBaseline_toTopOf,0,V8026d6512,38026d6542,;:;layout_constraintBaseline_toTopOf,0,V8037c8fd2,38037c9002,;:;layout_constraintBaseline_toTopOf,0,V804a5bdfd,3804a5be2d,;:;region_widthMoreThan,0,V805ddf026,3e05ddf05c,;dimension:;android\:orientation,0,V8017f41e0,2a017f4202,;:;android\:orientation,0,V8022459d3,2a022459f5,;:;android\:orientation,0,V802ae720d,2a02ae722f,;:;android\:orientation,0,V8034c8806,2a034c8828,;:;android\:orientation,0,V804bbc256,2a04bbc278,;:;textOutlineColor,0,V4013a3639,32013a3667,;color:;textOutlineColor,0,V80510d1a8,270510d1c7,;:;textOutlineColor,0,V8058fe6be,27058fe6dd,;:;constraints,0,V8057ee3fe,35057ee42b,;reference:;constraints,0,V805e0f0e5,2205e0f0ff,;:;minHeight,0,V804d3c72e,3304d3c759,;dimension:;ifTagSet,0,V805f0f385,3205f0f3af,;reference:;flow_horizontalStyle,0,V400530de7,b00570ea2,;enum:spread:0,spread_inside:1,packed:2,;flow_horizontalStyle,0,V801fe543f,2b01fe5462,;:;flow_horizontalStyle,0,V802375c77,2b02375c9a,;:;flow_horizontalStyle,0,V8031d80ec,2b031d810f,;:;flow_horizontalStyle,0,V803af9968,2b03af998b,;:;grid_verticalGaps,0,V803eda364,3b03eda397,;dimension:;mock_diagonalsColor,0,V804dcc8d1,3904dcc902,;color:;flow_lastVerticalStyle,0,V4005f0fdd,b0063109a,;enum:spread:0,spread_inside:1,packed:2,;flow_lastVerticalStyle,0,V8021156e4,2d02115709,;:;flow_lastVerticalStyle,0,V802516047,2d0251606c,;:;flow_lastVerticalStyle,0,V803308391,2d033083b6,;:;flow_lastVerticalStyle,0,V803c29c0d,2d03c29c32,;:;deriveConstraintsFrom,0,V803438676,3f034386ad,;reference:;customStringValue,0,V803db9fe8,3803dba018,;string:;circularflow_viewCenter,0,V400210592,3d002105cb,;reference:;circularflow_viewCenter,0,V80257612c,2e02576152,;:;barrierMargin,0,V4001803a5,33001803d4,;dimension:;barrierMargin,0,V801f352c0,2401f352dc,;:;barrierMargin,0,V802325bcb,2402325be7,;:;barrierMargin,0,V803147fa0,2403147fbc,;:;barrierMargin,0,V803a7984d,2403a79869,;:;barrierMargin,0,V804cec63f,2404cec65b,;:;curveFit,0,V4002606ad,b0029072d,;enum:spline:0,linear:1,;curveFit,0,V80405a7b5,1f0405a7cc,;:;curveFit,0,V8041caaba,1f041caad1,;:;curveFit,0,V80452b176,1f0452b18d,;:;curveFit,0,V8045ab28f,1f045ab2a6,;:;grid_useRtl,0,V803efa3dc,3303efa407,;boolean:;mock_labelColor,0,V804dac85b,3504dac888,;color:;layout_constraintTop_creator,0,V400d023b6,4000d023f2,;integer:;layout_constraintTop_creator,0,V801e55009,3301e55034,;:;layout_constraintTop_creator,0,V802956cea,3302956d15,;:;layout_constraintTop_creator,0,V803067ce9,3303067d14,;:;layout_constraintTop_creator,0,V803979533,330397955e,;:;layout_constraintTop_creator,0,V804c1c389,3304c1c3b4,;:;onPositiveCross,0,V80476b651,360476b67f,;string:;layout_constraintRight_toLeftOf,0,V400c32185,b00c521fe,;enum|reference:parent:0,;layout_constraintRight_toLeftOf,0,V801c3491f,3601c3494d,;:;layout_constraintRight_toLeftOf,0,V802666389,36026663b7,;:;layout_constraintRight_toLeftOf,0,V803758e49,3603758e77,;:;layout_constraintRight_toLeftOf,0,V8049ebc74,36049ebca2,;:;layout_constraintVertical_weight,0,V400dd2601,4200dd263f,;float:;layout_constraintVertical_weight,0,V801eb5154,3701eb5183,;:;layout_constraintVertical_weight,0,V8029b6e35,37029b6e64,;:;layout_constraintVertical_weight,0,V8030c7e34,37030c7e63,;:;layout_constraintVertical_weight,0,V8039d967e,37039d96ad,;:;layout_constraintVertical_weight,0,V804c7c4d4,3704c7c503,;:;layout_constraintHorizontal_weight,0,V400ba1fc0,4400ba2000,;float:;layout_constraintHorizontal_weight,0,V801ea511a,3901ea514b,;:;layout_constraintHorizontal_weight,0,V8029a6dfb,39029a6e2c,;:;layout_constraintHorizontal_weight,0,V8030b7dfa,39030b7e2b,;:;layout_constraintHorizontal_weight,0,V8039c9644,39039c9675,;:;layout_constraintHorizontal_weight,0,V804c6c49a,3904c6c4cb,;:;android\:textColor,0,V80503cf76,280503cf96,;:;mock_showDiagonals,0,V804ddc90b,3a04ddc93d,;boolean:;perpendicularPath_percent,0,V40124328f,3b012432c6,;float:;layout_constraintBottom_creator,0,V4008e179d,43008e17dc,;integer:;layout_constraintBottom_creator,0,V801e75073,3601e750a1,;:;layout_constraintBottom_creator,0,V802976d54,3602976d82,;:;layout_constraintBottom_creator,0,V803087d53,3603087d81,;:;layout_constraintBottom_creator,0,V80399959d,36039995cb,;:;layout_constraintBottom_creator,0,V804c3c3f3,3604c3c421,;:;barrierAllowsGoneWidgets,0,V4000f0245,3c000f027d,;boolean:;barrierAllowsGoneWidgets,0,V801fb53eb,2f01fb5412,;:;barrierAllowsGoneWidgets,0,V802315b9b,2f02315bc2,;:;barrierAllowsGoneWidgets,0,V8031a8098,2f031a80bf,;:;barrierAllowsGoneWidgets,0,V803ac9914,2f03ac993b,;:;barrierAllowsGoneWidgets,0,V804d5c795,2f04d5c7bc,;:;pathMotionArc,0,V4011b312c,b0122325b,;enum:none:0,startVertical:1,startHorizontal:2,flip:3,below:4,above:5,;pathMotionArc,0,V8021857ec,2402185808,;:;pathMotionArc,0,V803378499,24033784b5,;:;pathMotionArc,0,V803ca9d16,2403ca9d32,;:;pathMotionArc,0,V80451b151,240451b16d,;:;pathMotionArc,0,V804e4ca39,2404e4ca55,;:;pathMotionArc,0,V805bfeb4f,2405bfeb6b,;:;pathMotionArc,0,V805eaf264,2405eaf280,;:;blendSrc,0,V803f2a458,3203f2a482,;reference:;maxVelocity,0,V8054bdbe6,31054bdc0f,;float:;circularflow_angles,0,V4001d04a3,36001d04d5,;string:;circularflow_angles,0,V802566101,2a02566123,;:;android\:visibility,0,V801834237,2901834258,;:;android\:visibility,0,V802295aa0,2902295ac1,;:;android\:visibility,0,V802b37288,2902b372a9,;:;android\:visibility,0,V8034e8853,29034e8874,;:;android\:visibility,0,V80572e2c3,290572e2e4,;:;rotationCenterId,0,V8055ade97,3a055adec9,;reference:;dragDirection,0,V4002d07cb,b0036095d,;enum:dragUp:0,dragDown:1,dragLeft:2,dragRight:3,dragStart:4,dragEnd:5,dragClockwise:6,dragAnticlockwise:7,;dragDirection,0,V80557de26,240557de42,;:;motion_postLayoutCollision,0,V8047eb7f7,42047eb831,;boolean:;grid_validateInputs,0,V803eea3a0,3b03eea3d3,;boolean:;showPaths,0,V80524d563,310524d58c,;boolean:;pivotAnchor,0,V4012532cb,b01273330,;enum|reference:parent:0,;pivotAnchor,0,V8019a43e1,22019a43fb,;:;pivotAnchor,0,V802ca7432,2202ca744c,;:;pivotAnchor,0,V803568991,22035689ab,;:;carousel_previousState,0,V8016e3e72,40016e3eaa,;reference:;triggerReceiver,0,V80474b5e0,390474b611,;reference:;telltales_tailScale,0,V80538d8bd,390538d8ee,;float:;defaultState,0,V80581e475,360581e4a3,;reference:;dragThreshold,0,V80546db09,330546db34,;float:;android\:maxHeight,0,V801f75349,2801f75369,;:;android\:maxHeight,0,V802285a77,2802285a97,;:;android\:maxHeight,0,V803167ff6,2803168016,;:;android\:maxHeight,0,V803a89872,2803a89892,;:;arcMode,0,V4000a018b,b000e0240,;enum:startVertical:0,startHorizontal:1,flip:2,;mock_labelBackgroundColor,0,V804dbc891,3f04dbc8c8,;color:;motionPathRotate,0,V401102f3a,3201102f68,;float:;motionPathRotate,0,V804e5ca5e,2704e5ca7d,;:;percentWidth,0,V8044ab06f,32044ab099,;float:;textureEffect,0,V8051ad3c8,f051dd456,;enum:none:0,frost:1,;layout_constraintHeight_min,0,V400b01df0,b00b21e64,;dimension|enum:wrap:-2,;layout_constraintHeight_min,0,V801e14f37,3201e14f61,;:;layout_constraintHeight_min,0,V802916c18,3202916c42,;:;layout_constraintHeight_min,0,V803027c17,3203027c41,;:;layout_constraintHeight_min,0,V803939461,320393948b,;:;layout_constraintHeight_min,0,V804bdc2b7,3204bdc2e1,;:;android\:paddingBottom,0,V8023f5dc1,2c023f5de5,;:;maxHeight,0,V804d1c6c7,3304d1c6f2,;dimension:;android\:translationZ,0,V801a54503,2b01a54526,;:;android\:translationZ,0,V802d57554,2b02d57577,;:;android\:translationZ,0,V8035d8ab6,2b035d8ad9,;:;android\:translationZ,0,V80418aa29,2b0418aa4c,;:;android\:translationZ,0,V8042fad8f,2b042fadb2,;:;android\:translationZ,0,V8046fb528,2b046fb54b,;:;android\:translationZ,0,V805b5e9da,3e05b5ea10,;dimension:;onHide,0,V804ffcedf,2e04ffcf05,;boolean:;springDamping,0,V8054fdcb5,33054fdce0,;float:;layout_goneMarginBottom,0,V400f32a05,3d00f32a3e,;dimension:;layout_goneMarginBottom,0,V801d34c7c,2e01d34ca2,;:;layout_goneMarginBottom,0,V8027666e6,2e0276670c,;:;layout_goneMarginBottom,0,V802f4795c,2e02f47982,;:;layout_goneMarginBottom,0,V8038591a6,2e038591cc,;:;layout_goneMarginBottom,0,V804aebfd1,2e04aebff7,;:;android\:translationY,0,V801a344ce,2b01a344f1,;:;android\:translationY,0,V802d3751f,2b02d37542,;:;android\:translationY,0,V8035c8a8a,2b035c8aad,;:;android\:translationY,0,V80417a9fd,2b0417aa20,;:;android\:translationY,0,V8042ead63,2b042ead86,;:;android\:translationY,0,V8046eb4fc,2b046eb51f,;:;android\:translationY,0,V805b2e991,3e05b2e9c7,;dimension:;android\:translationX,0,V801a14499,2b01a144bc,;:;android\:translationX,0,V802d174ea,2b02d1750d,;:;android\:translationX,0,V8035b8a5e,2b035b8a81,;:;android\:translationX,0,V80416a9d1,2b0416a9f4,;:;android\:translationX,0,V8042dad37,2b042dad5a,;:;android\:translationX,0,V8046db4d0,2b046db4f3,;:;android\:translationX,0,V805afe948,3e05afe97e,;dimension:;carousel_infinite,0,V801703ef0,3901703f21,;boolean:;imageZoom,0,V803fea69d,2f03fea6c4,;float:;layout_constrainedHeight,0,V400821556,3c0082158e,;boolean:;layout_constrainedHeight,0,V801d94d92,2f01d94db9,;:;layout_constrainedHeight,0,V802896a73,2f02896a9a,;:;layout_constrainedHeight,0,V802fa7a72,2f02fa7a99,;:;layout_constrainedHeight,0,V8038b92bc,2f038b92e3,;:;layout_constrainedHeight,0,V804b4c0e7,2f04b4c10e,;:;onNegativeCross,0,V80475b61a,360475b648,;string:;flow_firstVerticalBias,0,V400460bc7,3800460bfb,;float:;flow_firstVerticalBias,0,V8020e5685,2d020e56aa,;:;flow_firstVerticalBias,0,V8024e5fe8,2d024e600d,;:;flow_firstVerticalBias,0,V8032d8332,2d032d8357,;:;flow_firstVerticalBias,0,V803bf9bae,2d03bf9bd3,;:;android\:fontFamily,0,V80505cfc7,290505cfe8,;:;android\:fontFamily,0,V80586e53c,290586e55d,;:;circleRadius,0,V4001c0470,32001c049e,;dimension:;textBackgroundPanY,0,V80517d31b,380517d34b,;float:;layout_constraintBottom_toBottomOf,0,V4008f17e1,b0091185d,;enum|reference:parent:0,;layout_constraintBottom_toBottomOf,0,V801c84a30,3901c84a61,;:;layout_constraintBottom_toBottomOf,0,V8026b649a,39026b64cb,;:;layout_constraintBottom_toBottomOf,0,V8037a8f5a,39037a8f8b,;:;layout_constraintBottom_toBottomOf,0,V804a3bd85,3904a3bdb6,;:;textBackgroundPanX,0,V80516d2e2,380516d312,;float:;layout_goneMarginBaseline,0,V400f229c5,3f00f22a00,;dimension:;layout_goneMarginBaseline,0,V801d64d05,3001d64d2d,;:;layout_goneMarginBaseline,0,V80279676f,3002796797,;:;layout_goneMarginBaseline,0,V802f779e5,3002f77a0d,;:;layout_goneMarginBaseline,0,V80388922f,3003889257,;:;layout_goneMarginBaseline,0,V804b1c05a,3004b1c082,;:;android\:pivotX,0,V8035789b4,25035789d1,;:;grid_horizontalGaps,0,V803eca326,3d03eca35b,;dimension:;android\:pivotY,0,V8035889da,25035889f7,;:;layout_constraintWidth_max,0,V400e92818,b00eb288b,;dimension|enum:wrap:-2,;layout_constraintWidth_max,0,V801df4ecf,3101df4ef8,;:;layout_constraintWidth_max,0,V8028f6bb0,31028f6bd9,;:;layout_constraintWidth_max,0,V803007baf,3103007bd8,;:;layout_constraintWidth_max,0,V8039193f9,3103919422,;:;layout_constraintWidth_max,0,V804bac224,3104bac24d,;:;barrierDirection,0,V400100282,b001703a0,;enum:left:0,right:1,top:2,bottom:3,start:5,end:6,;barrierDirection,0,V801f25298,2701f252b7,;:;barrierDirection,0,V802305b73,2702305b92,;:;barrierDirection,0,V803137f78,2703137f97,;:;barrierDirection,0,V803a497c2,2703a497e1,;:;barrierDirection,0,V804cdc617,2704cdc636,;:;currentState,0,V80521d4cd,360521d4fb,;reference:;android\:layout_marginRight,0,V801b74723,3101b7474c,;:;android\:layout_marginRight,0,V80283694d,3102836976,;:;android\:layout_marginRight,0,V802e77774,3102e7779d,;:;android\:layout_marginRight,0,V803688c4c,3103688c75,;:;android\:layout_marginRight,0,V80493ba79,310493baa2,;:;quantizeMotionPhase,0,V40136356e,350136359f,;float:;quantizeMotionPhase,0,V802205926,2a02205948,;:;quantizeMotionPhase,0,V8033f85d3,2a033f85f5,;:;quantizeMotionPhase,0,V804e9caf6,2a04e9cb18,;:;carousel_emptyViewsBehavior,0,V8017940f4,f017c4194,;enum:invisible:4,gone:8,;mock_label,0,V804d9c829,3104d9c852,;string:;android\:paddingEnd,0,V802435e71,2902435e92,;:;textureWidth,0,V80513d239,360513d267,;dimension:;android\:layout_marginStart,0,V801ad4600,3101ad4629,;:;android\:layout_marginStart,0,V8028669e2,3102866a0b,;:;android\:layout_marginStart,0,V802dd7651,3102dd767a,;:;android\:layout_marginStart,0,V803628b4d,3103628b76,;:;android\:layout_marginStart,0,V80489b97e,310489b9a7,;:;viewTransitionOnPositiveCross,0,V80479b6ff,470479b73e,;reference:;motionInterpolator,0,V805c7eca7,f05cfee35,;enum|reference|string:easeInOut:0,easeIn:1,easeOut:2,linear:3,bounce:4,overshoot:5,anticipate:6,;motionInterpolator,0,V805ebf289,2905ebf2aa,;:;springStopThreshold,0,V80550dce9,390550dd1a,;float:;carousel_firstView,0,V8016d3e35,3c016d3e69,;reference:;layoutDuringTransition,0,V4007c1488,b00811551,;enum:ignoreRequest:0,honorRequest:1,callMeasure:2,;layoutDuringTransition,0,V8052ed6fe,2d052ed723,;:;layoutDuringTransition,0,V805bdeb20,2d05bdeb45,;:;android\:textStyle,0,V8050bd0ca,28050bd0ea,;:;android\:textStyle,0,V8058ce635,28058ce655,;:;customReference,0,V803dfa0d0,3903dfa101,;reference:;percentHeight,0,V8044db0ac,33044db0d7,;float:;flow_verticalGap,0,V4006d121d,36006d124f,;dimension:;flow_verticalGap,0,V8020755ba,27020755d9,;:;flow_verticalGap,0,V8023c5d48,27023c5d67,;:;flow_verticalGap,0,V803268267,2703268286,;:;flow_verticalGap,0,V803b89ae3,2703b89b02,;:;constraint_referenced_ids,0,V400230604,3c0023063c,;string:;constraint_referenced_ids,0,V801f452e5,3001f4530d,;:;constraint_referenced_ids,0,V802335bf0,3002335c18,;:;constraint_referenced_ids,0,V803157fc5,3003157fed,;:;constraint_referenced_ids,0,V803a597ea,3003a59812,;:;constraint_referenced_ids,0,V804cfc664,3004cfc68c,;:;waveVariesBy,0,V401663d2a,b01693dae,;enum:position:0,path:1,;waveVariesBy,0,V80424abd3,230424abee,;:;layout_constraintTag,0,V400cf237e,3700cf23b1,;string:;layout_constraintTag,0,V8021d58aa,2b021d58cd,;:;layout_constraintTag,0,V802a06f45,2b02a06f68,;:;layout_constraintTag,0,V8033c8557,2b033c857a,;:;layout_constraintTag,0,V803d19e24,2b03d19e47,;:;layout_constraintTag,0,V8057ae372,2b057ae395,;:;springStiffness,0,V8054edc7f,35054edcac,;float:;layout_constraintWidth_percent,0,V400ef2908,4000ef2944,;float:;layout_constraintWidth_percent,0,V801e04f01,3501e04f2e,;:;layout_constraintWidth_percent,0,V802906be2,3502906c0f,;:;layout_constraintWidth_percent,0,V803017be1,3503017c0e,;:;layout_constraintWidth_percent,0,V80392942b,3503929458,;:;layout_constraintWidth_percent,0,V804bcc281,3504bcc2ae,;:;layout_optimizationLevel,0,V400fa2bab,b01092e3a,;flags:none:0,legacy:0,standard:257,direct:1,barrier:2,chains:4,dimensions:8,ratio:16,groups:32,graph:64,graph_wrap:128,cache_measures:256,dependency_ordering:512,grouping:1024,;layout_optimizationLevel,0,V8022c5af4,2f022c5b1b,;:;animateRelativeTo,0,V400090153,3700090186,;reference:;animateRelativeTo,0,V80215576f,280215578f,;:;animateRelativeTo,0,V80334841c,280334843c,;:;animateRelativeTo,0,V803c69c98,2803c69cb8,;:;animateRelativeTo,0,V804e1c9bc,2804e1c9dc,;:;textFillColor,0,V8058de65e,33058de689,;color:;region_widthLessThan,0,V805dcefe7,3e05dcf01d,;dimension:;overlay,0,V803faa5da,2f03faa601,;boolean:;motionEffect_alpha,0,V804f2cc95,3804f2ccc5,;float:;defaultDuration,0,V4002a0732,33002a0761,;integer:;defaultDuration,0,V8052dd6d7,26052dd6f5,;:;layout_constraintHeight_default,0,V400a81cc2,b00ac1d72,;enum:spread:0,wrap:1,percent:2,;layout_constraintHeight_default,0,V801dd4e66,3601dd4e94,;:;layout_constraintHeight_default,0,V8028d6b47,36028d6b75,;:;layout_constraintHeight_default,0,V802fe7b46,3602fe7b74,;:;layout_constraintHeight_default,0,V8038f9390,36038f93be,;:;layout_constraintHeight_default,0,V804b8c1bb,3604b8c1e9,;:;android\:layout_marginBottom,0,V801af463b,3201af4665,;:;android\:layout_marginBottom,0,V8028569af,32028569d9,;:;android\:layout_marginBottom,0,V802df768c,3202df76b6,;:;android\:layout_marginBottom,0,V803648b88,3203648bb2,;:;android\:layout_marginBottom,0,V8048bb9b1,32048bb9db,;:;staggered,0,V805d2ee5f,2f05d2ee86,;float:;layout_constraintStart_toStartOf,0,V400cc22ff,b00ce2379,;enum|reference:parent:0,;layout_constraintStart_toStartOf,0,V801cd4b53,3701cd4b82,;:;layout_constraintStart_toStartOf,0,V8027065bd,37027065ec,;:;layout_constraintStart_toStartOf,0,V8037f907d,37037f90ac,;:;layout_constraintStart_toStartOf,0,V804a8bea8,3704a8bed7,;:;motionEffect_start,0,V804eecb99,3a04eecbcb,;integer:;constraintSet,0,V4002205d0,33002205ff,;reference:;constraintSet,0,V8022f5b4e,24022f5b6a,;:;constraintSet,0,V805f4f42e,2405f4f44a,;:;layout_constraintStart_toEndOf,0,V400c92282,b00cb22fa,;enum|reference:parent:0,;layout_constraintStart_toEndOf,0,V801cc4b1d,3501cc4b4a,;:;layout_constraintStart_toEndOf,0,V8026f6587,35026f65b4,;:;layout_constraintStart_toEndOf,0,V8037e9047,35037e9074,;:;layout_constraintStart_toEndOf,0,V804a7be72,3504a7be9f,;:;android\:paddingLeft,0,V802405dee,2a02405e10,;:;android\:minWidth,0,V801fa53c3,2701fa53e2,;:;android\:minWidth,0,V8022559fe,2702255a1d,;:;android\:minWidth,0,V803198070,270319808f,;:;android\:minWidth,0,V803ab98ec,2703ab990b,;:;android\:padding,0,V8023d5d70,26023d5d8e,;:;dragScale,0,V80545dad9,2f0545db00,;float:;maxAcceleration,0,V8054cdc18,35054cdc45,;float:;textBackground,0,V80511d1d0,380511d200,;reference:;layoutDescription,0,V4007b1450,37007b1483,;reference:;layoutDescription,0,V8022d5b24,28022d5b44,;:;layoutDescription,0,V80520d4a4,280520d4c4,;:;circularflow_defaultAngle,0,V4001e04da,3b001e0511,;float:;circularflow_defaultAngle,0,V80259618d,30025961b5,;:;reactiveGuide_animateChange,0,V802a5703b,4302a57076,;boolean:;android\:textSize,0,V80504cf9f,270504cfbe,;:;android\:textSize,0,V80585e514,270585e533,;:;quantizeMotionInterpolator,0,V4012e341f,b01353569,;enum|reference|string:easeInOut:0,easeIn:1,easeOut:2,linear:3,bounce:4,overshoot:5,;quantizeMotionInterpolator,0,V802215951,310221597a,;:;quantizeMotionInterpolator,0,V8034085fe,3103408627,;:;quantizeMotionInterpolator,0,V804eacb21,3104eacb4a,;:;autoTransition,0,V805c0eb74,f05c6ec9e,;enum:none:0,jumpToStart:1,jumpToEnd:2,animateToStart:3,animateToEnd:4,;carousel_forwardTransition,0,V801713f2a,4401713f66,;reference:;transitionDisable,0,V401473860,3501473891,;boolean:;transitionDisable,0,V805bceaf7,2805bceb17,;:;transitionDisable,0,V805e9f23b,2805e9f25b,;:;waveDecay,0,V401593b10,2d01593b39,;integer:;waveDecay,0,V80462b365,200462b37d,;:;transitionFlags,0,V805d4ee90,f05d9ef9e,;flags:none:0,beginOnFirstDraw:1,disableIntraAutoTransition:2,onInterceptTouchReturnSwipe:4,;layout_constraintTop_toTopOf,0,V400d42475,b00d624eb,;enum|reference:parent:0,;layout_constraintTop_toTopOf,0,V801c5498e,3301c549b9,;:;layout_constraintTop_toTopOf,0,V8026863f8,3302686423,;:;layout_constraintTop_toTopOf,0,V803778eb8,3303778ee3,;:;layout_constraintTop_toTopOf,0,V804a0bce3,3304a0bd0e,;:;content,0,V40025067f,2d002506a8,;reference:;content,0,V802ab71a3,1e02ab71b9,;:;onCross,0,V80477b688,2e0477b6ae,;string:;duration,0,V4003f0a9a,2c003f0ac2,;integer:;duration,0,V805d1ee3f,1f05d1ee56,;:;duration,0,V805e7f1e8,1f05e7f1ff,;:;layout_constraintBottom_toTopOf,0,V400921862,b009418db,;enum|reference:parent:0,;layout_constraintBottom_toTopOf,0,V801c749f9,3601c74a27,;:;layout_constraintBottom_toTopOf,0,V8026a6463,36026a6491,;:;layout_constraintBottom_toTopOf,0,V803798f23,3603798f51,;:;layout_constraintBottom_toTopOf,0,V804a2bd4e,3604a2bd7c,;:;layout_goneMarginStart,0,V400f72af7,3c00f72b2f,;dimension:;layout_goneMarginStart,0,V801d44cab,2d01d44cd0,;:;layout_goneMarginStart,0,V802776715,2d0277673a,;:;layout_goneMarginStart,0,V802f5798b,2d02f579b0,;:;layout_goneMarginStart,0,V8038691d5,2d038691fa,;:;layout_goneMarginStart,0,V804afc000,2d04afc025,;:;altSrc,0,V803f3a48b,3003f3a4b3,;reference:;attributeName,0,V803d59e99,3403d59ec5,;string:;customDimension,0,V803dca021,3903dca052,;dimension:;constraintRotate,0,V8034586e9,f034b87fd,;enum:none:0,right:1,left:2,x_right:3,x_left:4,;deltaPolarRadius,0,V4002c0798,32002c07c6,;float:;touchAnchorSide,0,V4013d36db,b01453820,;enum:top:0,left:1,right:2,bottom:3,middle:4,start:5,end:6,;touchAnchorSide,0,V80559de70,260559de8e,;:;carousel_backwardTransition,0,V801723f6f,4501723fac,;reference:;wavePeriod,0,V4015b3b75,2c015b3b9d,;float:;wavePeriod,0,V80422ab8f,210422aba8,;:;wavePeriod,0,V8045cb2d0,21045cb2e9,;:;layout_constraintBaseline_toBaselineOf,0,V400851615,b00871695,;enum|reference:parent:0,;layout_constraintBaseline_toBaselineOf,0,V801c94a6a,3d01c94a9f,;:;layout_constraintBaseline_toBaselineOf,0,V8026c64d4,3d026c6509,;:;layout_constraintBaseline_toBaselineOf,0,V8037b8f94,3d037b8fc9,;:;layout_constraintBaseline_toBaselineOf,0,V804a4bdbf,3d04a4bdf4,;:;android\:rotationY,0,V801934358,2801934378,;:;android\:rotationY,0,V802c373a9,2802c373c9,;:;android\:rotationY,0,V80353891c,280353893c,;:;android\:rotationY,0,V8040ea8a5,28040ea8c5,;:;android\:rotationY,0,V8042aacc2,28042aace2,;:;android\:rotationY,0,V80469b42f,280469b44f,;:;android\:rotationY,0,V8059ee816,28059ee836,;:;android\:layout_height,0,V801a9456d,2c01a94591,;:;android\:layout_height,0,V8027e6856,2c027e687a,;:;android\:layout_height,0,V802d975be,2c02d975e2,;:;android\:layout_height,0,V803608b17,2c03608b3b,;:;android\:layout_height,0,V80485b8f3,2c0485b917,;:;onShow,0,V804feceb0,2e04feced6,;boolean:;textBackgroundZoom,0,V80518d354,380518d384,;float:;layout_constraintGuide_begin,0,V4009f1adc,42009f1b1a,;dimension:;layout_constraintGuide_begin,0,V801bd47ef,3301bd481a,;:;layout_constraintGuide_begin,0,V802606259,3302606284,;:;layout_constraintGuide_begin,0,V802ec7811,3302ec783c,;:;layout_constraintGuide_begin,0,V8036e8d18,33036e8d43,;:;layout_constraintGuide_begin,0,V80498bb44,330498bb6f,;:;layout_wrapBehaviorInParent,0,V4010a2e3f,b010f2f35,;enum:included:0,horizontal_only:1,vertical_only:2,skipped:3,;layout_wrapBehaviorInParent,0,V801f05264,3201f0528e,;:;layout_wrapBehaviorInParent,0,V802a16f71,3202a16f9b,;:;layout_wrapBehaviorInParent,0,V803117f44,3203117f6e,;:;layout_wrapBehaviorInParent,0,V803a2978e,3203a297b8,;:;layout_wrapBehaviorInParent,0,V804ccc5e4,3204ccc60e,;:;textOutlineThickness,0,V4013b366c,3a013b36a2,;dimension:;textOutlineThickness,0,V8050fd17c,2b050fd19f,;:;textOutlineThickness,0,V8058ee692,2b058ee6b5,;:;grid_columnWeights,0,V803e7a249,3903e7a27a,;string:;springBoundary,0,V80551dd23,f0556de1d,;flags:overshoot:0,bounceStart:1,bounceEnd:2,bounceBoth:3,;carousel_nextState,0,V8016f3eb3,3c016f3ee7,;reference:;imagePanX,0,V803fca63d,2f03fca664,;float:;imagePanY,0,V803fda66d,2f03fda694,;float:;carousel_touchUp_velocityThreshold,0,V8017840ab,48017840eb,;float:;grid_rowWeights,0,V803e6a212,3603e6a240,;string:;brightness,0,V803f5a4ed,3003f5a515,;float:;android\:elevation,0,V8018a42c0,28018a42e0,;:;android\:elevation,0,V8022a5aca,28022a5aea,;:;android\:elevation,0,V802ba7311,2802ba7331,;:;android\:elevation,0,V8035088a2,28035088c2,;:;android\:elevation,0,V8040ba82b,28040ba84b,;:;android\:elevation,0,V80427ac48,280427ac68,;:;android\:elevation,0,V80466b3b5,280466b3d5,;:;android\:elevation,0,V80595e77e,280595e79e,;:;keyPositionType,0,V8043caee4,f0441afea,;enum:deltaRelative:0,pathRelative:1,parentRelative:2,axisRelative:3,;android\:shadowDy,0,V8058ae5e1,27058ae600,;:;applyMotionScene,0,V80523d52a,380523d55a,;boolean:;android\:rotationX,0,V801904325,2801904345,;:;android\:rotationX,0,V802c07376,2802c07396,;:;android\:rotationX,0,V8035288f3,2803528913,;:;android\:rotationX,0,V8040da87c,28040da89c,;:;android\:rotationX,0,V80429ac99,280429acb9,;:;android\:rotationX,0,V80468b406,280468b426,;:;android\:rotationX,0,V8059be7e3,28059be803,;:;android\:minHeight,0,V801f9539a,2801f953ba,;:;android\:minHeight,0,V802265a26,2802265a46,;:;android\:minHeight,0,V803188047,2803188067,;:;android\:minHeight,0,V803aa98c3,2803aa98e3,;:;customColorDrawableValue,0,V803d89f37,3e03d89f6d,;color:;customFloatValue,0,V803da9fb1,3603da9fdf,;float:;triggerSlack,0,V8047bb787,32047bb7b1,;float:;android\:transformPivotX,0,V8019c4405,2e019c442b,;:;android\:transformPivotX,0,V802cc7456,2e02cc747c,;:;android\:transformPivotX,0,V803598a00,2e03598a26,;:;android\:transformPivotX,0,V8040fa8ce,2e040fa8f4,;:;android\:transformPivotX,0,V805a7e8a9,2e05a7e8cf,;:;android\:layout_marginLeft,0,V801b546e9,3001b54711,;:;android\:layout_marginLeft,0,V80282691c,3002826944,;:;android\:layout_marginLeft,0,V802e5773a,3002e57762,;:;android\:layout_marginLeft,0,V803678c1b,3003678c43,;:;android\:layout_marginLeft,0,V80491ba47,300491ba6f,;:;layout_marginBaseline,0,V400f92b6f,3b00f92ba6,;dimension:;layout_marginBaseline,0,V801d74d36,2c01d74d5a,;:;layout_marginBaseline,0,V8027a67a0,2c027a67c4,;:;layout_marginBaseline,0,V802f87a16,2c02f87a3a,;:;layout_marginBaseline,0,V803899260,2c03899284,;:;layout_marginBaseline,0,V804b2c08b,2c04b2c0af,;:;quantizeMotionSteps,0,V4013735a4,37013735d7,;integer:;quantizeMotionSteps,0,V8021f58fb,2a021f591d,;:;quantizeMotionSteps,0,V8033e85a8,2a033e85ca,;:;quantizeMotionSteps,0,V803ce9db3,2a03ce9dd5,;:;quantizeMotionSteps,0,V804e8cacb,2a04e8caed,;:;customBoolean,0,V803dea09a,3503dea0c7,;boolean:;layout_constraintVertical_chainStyle,0,V400d82531,b00dc25fc,;enum:spread:0,spread_inside:1,packed:2,;layout_constraintVertical_chainStyle,0,V801ed51ca,3b01ed51fd,;:;layout_constraintVertical_chainStyle,0,V8029d6eab,3b029d6ede,;:;layout_constraintVertical_chainStyle,0,V8030e7eaa,3b030e7edd,;:;layout_constraintVertical_chainStyle,0,V8039f96f4,3b039f9727,;:;layout_constraintVertical_chainStyle,0,V804c9c54a,3b04c9c57d,;:;motionStagger,0,V401122f9e,2f01122fc9,;float:;motionStagger,0,V8021e58d6,24021e58f2,;:;motionStagger,0,V8033d8583,24033d859f,;:;motionStagger,0,V803cd9d8e,2403cd9daa,;:;motionStagger,0,V804e6ca86,2404e6caa2,;:;carousel_touchUp_dampeningFactor,0,V801733fb5,4601733ff3,;float:;flow_lastHorizontalStyle,0,V400590ee1,b005d0fa0,;enum:spread:0,spread_inside:1,packed:2,;flow_lastHorizontalStyle,0,V8021056b4,2f021056db,;:;flow_lastHorizontalStyle,0,V802506017,2f0250603e,;:;flow_lastHorizontalStyle,0,V8032f8361,2f032f8388,;:;flow_lastHorizontalStyle,0,V803c19bdd,2f03c19c04,;:;polarRelativeTo,0,V4012d33e9,35012d341a,;reference:;polarRelativeTo,0,V802195811,260219582f,;:;polarRelativeTo,0,V8033884be,26033884dc,;:;polarRelativeTo,0,V803cb9d3b,2603cb9d59,;:;android\:transformPivotY,0,V8019d4434,2e019d445a,;:;android\:transformPivotY,0,V802cd7485,2e02cd74ab,;:;android\:transformPivotY,0,V8035a8a2f,2e035a8a55,;:;android\:transformPivotY,0,V80410a8fd,2e0410a923,;:;android\:transformPivotY,0,V805aae8e2,2e05aae908,;:;layout_editor_absoluteY,0,V400f12987,3d00f129c0,;dimension:;layout_editor_absoluteY,0,V801ef5235,2e01ef525b,;:;layout_editor_absoluteY,0,V8029f6f16,2e029f6f3c,;:;layout_editor_absoluteY,0,V803107f15,2e03107f3b,;:;layout_editor_absoluteY,0,V803a1975f,2e03a19785,;:;layout_editor_absoluteY,0,V804cbc5b5,2e04cbc5db,;:;layout_editor_absoluteX,0,V400f02949,3d00f02982,;dimension:;layout_editor_absoluteX,0,V801ee5206,2e01ee522c,;:;layout_editor_absoluteX,0,V8029e6ee7,2e029e6f0d,;:;layout_editor_absoluteX,0,V8030f7ee6,2e030f7f0c,;:;layout_editor_absoluteX,0,V803a09730,2e03a09756,;:;layout_editor_absoluteX,0,V804cac586,2e04cac5ac,;:;android\:rotation,0,V8018d42f3,27018d4312,;:;android\:rotation,0,V802bd7344,2702bd7363,;:;android\:rotation,0,V8035188cb,27035188ea,;:;android\:rotation,0,V8040ca854,27040ca873,;:;android\:rotation,0,V80428ac71,270428ac90,;:;android\:rotation,0,V80467b3de,270467b3fd,;:;android\:rotation,0,V80598e7b1,270598e7d0,;:;layout_constraintEnd_toStartOf,0,V4009c1a5f,b009e1ad7,;enum|reference:parent:0,;layout_constraintEnd_toStartOf,0,V801ce4b8b,3501ce4bb8,;:;layout_constraintEnd_toStartOf,0,V8027165f5,3502716622,;:;layout_constraintEnd_toStartOf,0,V8038090b5,35038090e2,;:;layout_constraintEnd_toStartOf,0,V804a9bee0,3504a9bf0d,;:;android\:id,0,V80180420b,2101804224,;:;android\:id,0,V802af7238,2102af7251,;:;android\:id,0,V8034d8831,21034d884a,;:;android\:id,0,V8057de3dc,21057de3f5,;:;android\:id,0,V805b9ea5d,2105b9ea76,;:;android\:id,0,V805e3f14f,2105e3f168,;:;transitionEasing,0,V401483896,b014d397f,;enum|string:standard:0,accelerate:1,decelerate:2,linear:3,;transitionEasing,0,V8021757c4,27021757e3,;:;transitionEasing,0,V803368471,2703368490,;:;transitionEasing,0,V803c99cee,2703c99d0d,;:;transitionEasing,0,V80404a78d,270404a7ac,;:;transitionEasing,0,V8041eaaff,27041eab1e,;:;transitionEasing,0,V80450b129,270450b148,;:;transitionEasing,0,V80459b267,270459b286,;:;transitionEasing,0,V804e3ca11,2704e3ca30,;:;layout_constraintVertical_bias,0,V400d724f0,4000d7252c,;float:;layout_constraintVertical_bias,0,V801db4dfa,3501db4e27,;:;layout_constraintVertical_bias,0,V8028b6adb,35028b6b08,;:;layout_constraintVertical_bias,0,V802fc7ada,3502fc7b07,;:;layout_constraintVertical_bias,0,V8038d9324,35038d9351,;:;layout_constraintVertical_bias,0,V804b6c14f,3504b6c17c,;:;sizePercent,0,V4013835dc,2d01383605,;float:;sizePercent,0,V80454b1b6,220454b1d0,;:;flow_maxElementsWrap,0,V40064109f,38006410d3,;integer:;flow_maxElementsWrap,0,V802055564,2b02055587,;:;flow_maxElementsWrap,0,V8023a5cf2,2b023a5d15,;:;flow_maxElementsWrap,0,V803248211,2b03248234,;:;flow_maxElementsWrap,0,V803b69a8d,2b03b69ab0,;:;touchAnchorId,0,V4013c36a7,33013c36d6,;reference:;touchAnchorId,0,V80558de4b,240558de67,;:;layout_constraintHorizontal_bias,0,V400b41eab,4200b41ee9,;float:;layout_constraintHorizontal_bias,0,V801da4dc2,3701da4df1,;:;layout_constraintHorizontal_bias,0,V8028a6aa3,37028a6ad2,;:;layout_constraintHorizontal_bias,0,V802fb7aa2,3702fb7ad1,;:;layout_constraintHorizontal_bias,0,V8038c92ec,37038c931b,;:;layout_constraintHorizontal_bias,0,V804b5c117,3704b5c146,;:;framePosition,0,V4007913ea,3100791417,;integer:;framePosition,0,V80402a744,240402a760,;:;framePosition,0,V8041daada,24041daaf6,;:;framePosition,0,V8044eb0e0,24044eb0fc,;:;framePosition,0,V80457b21e,240457b23a,;:;framePosition,0,V80472b597,240472b5b3,;:;android\:paddingTop,0,V8023e5d97,29023e5db8,;:;maxWidth,0,V804d2c6fb,3204d2c725,;dimension:;flow_wrapMode,0,V400731312,b007813e5,;enum:none:0,chain:1,aligned:2,chain2:3,;flow_wrapMode,0,V80204553f,240204555b,;:;flow_wrapMode,0,V802395ccd,2402395ce9,;:;flow_wrapMode,0,V8032381ec,2403238208,;:;flow_wrapMode,0,V803b59a68,2403b59a84,;:;flow_verticalBias,0,V4006c11e9,33006c1218,;float:;flow_verticalBias,0,V8020254eb,280202550b,;:;flow_verticalBias,0,V802465ef1,2802465f11,;:;flow_verticalBias,0,V803218198,28032181b8,;:;flow_verticalBias,0,V803b39a14,2803b39a34,;:;roundPercent,0,V803fba60a,3203fba634,;float:;targetId,0,V40139360a,2e01393634,;reference:;targetId,0,V8053bd937,1f053bd94e,;:;warmth,0,V803f6a51e,2c03f6a542,;float:;borderRound,0,V4001903d9,3100190406,;dimension:;borderRound,0,V8050cd0f3,22050cd10d,;:;borderRound,0,V80590e6e6,220590e700,;:;transitionPathRotate,0,V4014e3984,36014e39b6,;float:;transitionPathRotate,0,V8021a5838,2b021a585b,;:;transitionPathRotate,0,V8033984e5,2b03398508,;:;transitionPathRotate,0,V803cc9d62,2b03cc9d85,;:;transitionPathRotate,0,V80413a959,2b0413a97c,;:;transitionPathRotate,0,V80425abf7,2b0425ac1a,;:;transitionPathRotate,0,V8046ab458,2b046ab47b,;:;wavePhase,0,V4015c3ba2,2b015c3bc9,;float:;wavePhase,0,V80421ab6e,200421ab86,;:;wavePhase,0,V8045fb33a,20045fb352,;:;percentY,0,V80447b036,2e0447b05c,;float:;layout_constraintLeft_toRightOf,0,V400bf20c4,b00c1213d,;enum|reference:parent:0,;layout_constraintLeft_toRightOf,0,V801c248e8,3601c24916,;:;layout_constraintLeft_toRightOf,0,V802656352,3602656380,;:;layout_constraintLeft_toRightOf,0,V803748e12,3603748e40,;:;layout_constraintLeft_toRightOf,0,V8049dbc3d,36049dbc6b,;:;motionDebug,0,V80525d595,f052ad68a,;enum:NO_DEBUG:0,SHOW_PROGRESS:1,SHOW_PATH:2,SHOW_ALL:3,;flow_firstHorizontalStyle,0,V400410b02,b00450bc2,;enum:spread:0,spread_inside:1,packed:2,;flow_firstHorizontalStyle,0,V8020b55f5,30020b561d,;:;flow_firstHorizontalStyle,0,V8024b5f58,30024b5f80,;:;flow_firstHorizontalStyle,0,V8032a82a2,30032a82ca,;:;flow_firstHorizontalStyle,0,V803bc9b1e,3003bc9b46,;:;android\:autoSizeTextType,0,V80512d209,2f0512d230,;:;layout_constraintRight_creator,0,V400c22142,4200c22180,;integer:;layout_constraintRight_creator,0,V801e6503d,3501e6506a,;:;layout_constraintRight_creator,0,V802966d1e,3502966d4b,;:;layout_constraintRight_creator,0,V803077d1d,3503077d4a,;:;layout_constraintRight_creator,0,V803989567,3503989594,;:;layout_constraintRight_creator,0,V804c2c3bd,3504c2c3ea,;:;contrast,0,V803f7a54b,2e03f7a571,;float:;percentX,0,V80444affd,2e0444b023,;float:;motionEffect_viewTransition,0,V804f3ccce,4504f3cd0b,;reference:;layout_goneMarginLeft,0,V400f52a7e,3b00f52ab5,;dimension:;layout_goneMarginLeft,0,V801d04bf5,2c01d04c19,;:;layout_goneMarginLeft,0,V80273665f,2c02736683,;:;layout_goneMarginLeft,0,V802f178d5,2c02f178f9,;:;layout_goneMarginLeft,0,V80382911f,2c03829143,;:;layout_goneMarginLeft,0,V804abbf4a,2c04abbf6e,;:;layout_goneMarginRight,0,V400f62aba,3c00f62af2,;dimension:;layout_goneMarginRight,0,V801d24c4e,2d01d24c73,;:;layout_goneMarginRight,0,V8027566b8,2d027566dd,;:;layout_goneMarginRight,0,V802f3792e,2d02f37953,;:;layout_goneMarginRight,0,V803849178,2d0384919d,;:;layout_goneMarginRight,0,V804adbfa3,2d04adbfc8,;:;imageRotate,0,V803ffa6cd,3103ffa6f6,;float:;android\:gravity,0,V80506cff1,260506d00f,;:;layout_constraintHeight_percent,0,V400b31e69,4100b31ea6,;float:;layout_constraintHeight_percent,0,V801e34f9d,3601e34fcb,;:;layout_constraintHeight_percent,0,V802936c7e,3602936cac,;:;layout_constraintHeight_percent,0,V803047c7d,3603047cab,;:;layout_constraintHeight_percent,0,V8039594c7,36039594f5,;:;layout_constraintHeight_percent,0,V804bfc31d,3604bfc34b,;:;visibilityMode,0,V401553a85,b01583b0b,;enum:normal:0,ignore:1,;visibilityMode,0,V801844261,250184427e,;:;visibilityMode,0,V802b472b2,2502b472cf,;:;visibilityMode,0,V80573e2ed,250573e30a,;:;drawPath,0,V400370962,b003e0a95,;enum:none:0,path:1,pathRelative:2,deltaRelative:3,asConfigured:4,rectangles:5,;drawPath,0,V8021b5864,1f021b587b,;:;drawPath,0,V8033a8511,1f033a8528,;:;drawPath,0,V803cf9dde,1f03cf9df5,;:;drawPath,0,V80453b196,1f0453b1ad,;:;drawPath,0,V804e7caab,1f04e7cac2,;:;constraintSetEnd,0,V805bbeabc,3a05bbeaee,;reference:;layout_constraintHeight_max,0,V400ad1d77,b00af1deb,;dimension|enum:wrap:-2,;layout_constraintHeight_max,0,V801e24f6a,3201e24f94,;:;layout_constraintHeight_max,0,V802926c4b,3202926c75,;:;layout_constraintHeight_max,0,V803037c4a,3203037c74,;:;layout_constraintHeight_max,0,V803949494,32039494be,;:;layout_constraintHeight_max,0,V804bec2ea,3204bec314,;:;guidelineUseRtl,0,V4007a141c,33007a144b,;boolean:;guidelineUseRtl,0,V801c0488b,2601c048a9,;:;guidelineUseRtl,0,V8026362f5,2602636313,;:;guidelineUseRtl,0,V802ef78ad,2602ef78cb,;:;guidelineUseRtl,0,V803718db4,2603718dd2,;:;guidelineUseRtl,0,V8049bbbe0,26049bbbfe,;:;telltales_tailColor,0,V80537d883,390537d8b4,;color:;flow_firstHorizontalBias,0,V400400ac7,3a00400afd,;float:;flow_firstHorizontalBias,0,V8020d5655,2f020d567c,;:;flow_firstHorizontalBias,0,V8024d5fb8,2f024d5fdf,;:;flow_firstHorizontalBias,0,V8032c8302,2f032c8329,;:;flow_firstHorizontalBias,0,V803be9b7e,2f03be9ba5,;:;android\:layout_width,0,V801a74538,2b01a7455b,;:;android\:layout_width,0,V8027d682a,2b027d684d,;:;android\:layout_width,0,V802d77589,2b02d775ac,;:;android\:layout_width,0,V8035e8ae2,2b035e8b05,;:;android\:layout_width,0,V80484b8c7,2b0484b8ea,;:;flow_horizontalBias,0,V400510d78,3500510da9,;float:;flow_horizontalBias,0,V802035514,2a02035536,;:;flow_horizontalBias,0,V802475f1a,2a02475f3c,;:;flow_horizontalBias,0,V8032281c1,2a032281e3,;:;flow_horizontalBias,0,V803b49a3d,2a03b49a5f,;:;layout_constraintTop_toBottomOf,0,V400d123f7,b00d32470,;enum|reference:parent:0,;layout_constraintTop_toBottomOf,0,V801c649c2,3601c649f0,;:;layout_constraintTop_toBottomOf,0,V80269642c,360269645a,;:;layout_constraintTop_toBottomOf,0,V803788eec,3603788f1a,;:;layout_constraintTop_toBottomOf,0,V804a1bd17,3604a1bd45,;:;viewTransitionOnNegativeCross,0,V80478b6b7,470478b6f6,;reference:;onTouchUp,0,V80564e08e,f056de26c,;enum:autoComplete:0,autoCompleteToStart:1,autoCompleteToEnd:2,stop:3,decelerate:4,decelerateAndComplete:5,neverCompleteToStart:6,neverCompleteToEnd:7,;animateCircleAngleTo,0,V400020037,b0008014e,;enum:bestChoice:0,closest:1,clockwise:2,antiClockwise:3,constraint:4,;animateCircleAngleTo,0,V802165798,2b021657bb,;:;animateCircleAngleTo,0,V803358445,2b03358468,;:;animateCircleAngleTo,0,V803c79cc1,2b03c79ce4,;:;animateCircleAngleTo,0,V804e2c9e5,2b04e2ca08,;:;layout_constraintBaseline_creator,0,V4008415cf,4500841610,;integer:;layout_constraintBaseline_creator,0,V801e850aa,3801e850da,;:;layout_constraintBaseline_creator,0,V802986d8b,3802986dbb,;:;layout_constraintBaseline_creator,0,V803097d8a,3803097dba,;:;layout_constraintBaseline_creator,0,V8039a95d4,38039a9604,;:;layout_constraintBaseline_creator,0,V804c4c42a,3804c4c45a,;:;textureBlurFactor,0,V80515d2a8,390515d2d9,;integer:;touchRegionId,0,V8055bded2,37055bdf01,;reference:;customColorValue,0,V803d79f00,3603d79f2e,;color:;motionEffect_end,0,V804efcbd4,3804efcc04,;integer:;grid_rows,0,V803e2a147,3103e2a170,;integer:;layout_constraintWidth_min,0,V400ec2890,b00ee2903,;dimension|enum:wrap:-2,;layout_constraintWidth_min,0,V801de4e9d,3101de4ec6,;:;layout_constraintWidth_min,0,V8028e6b7e,31028e6ba7,;:;layout_constraintWidth_min,0,V802ff7b7d,3102ff7ba6,;:;layout_constraintWidth_min,0,V8039093c7,31039093f0,;:;layout_constraintWidth_min,0,V804b9c1f2,3104b9c21b,;:;grid_skips,0,V803e5a1e0,3103e5a209,;string:;flow_padding,0,V4006510d8,3200651106,;dimension:;minWidth,0,V804d4c762,3204d4c78c,;dimension:;layout_constraintHeight,0,V400a21ba1,b00a71cbd,;dimension|enum|string:match_parent:-1,wrap_content:-2,match_constraint:-3,wrap_content_constrained:-4,;layout_constraintHeight,0,V801ac45d1,2e01ac45f7,;:;layout_constraintHeight,0,V8027c67fb,2e027c6821,;:;layout_constraintHeight,0,V802dc7622,2e02dc7648,;:;layout_constraintHeight,0,V80487b94e,2e0487b974,;:;layout_goneMarginTop,0,V400f82b34,3a00f82b6a,;dimension:;layout_goneMarginTop,0,V801d14c22,2b01d14c45,;:;layout_goneMarginTop,0,V80274668c,2b027466af,;:;layout_goneMarginTop,0,V802f27902,2b02f27925,;:;layout_goneMarginTop,0,V80383914c,2b0383916f,;:;layout_goneMarginTop,0,V804acbf77,2b04acbf9a,;:;reactiveGuide_valueId,0,V802a46ffb,3f02a47032,;reference:;waveShape,0,V4015d3bce,b01653d25,;enum|string:sin:0,square:1,triangle:2,sawtooth:3,reverseSawtooth:4,cos:5,bounce:6,;waveShape,0,V80420ab4d,200420ab65,;:;waveShape,0,V8045bb2af,20045bb2c7,;:;android\:layout_marginHorizontal,0,V8028068b0,36028068de,;:;layout_constraintGuide_end,0,V400a01b1f,4000a01b5b,;dimension:;layout_constraintGuide_end,0,V801be4823,3101be484c,;:;layout_constraintGuide_end,0,V80261628d,31026162b6,;:;layout_constraintGuide_end,0,V802ed7845,3102ed786e,;:;layout_constraintGuide_end,0,V8036f8d4c,31036f8d75,;:;layout_constraintGuide_end,0,V80499bb78,310499bba1,;:;layout_constraintLeft_toLeftOf,0,V400bc2047,b00be20bf,;enum|reference:parent:0,;layout_constraintLeft_toLeftOf,0,V801c148b2,3501c148df,;:;layout_constraintLeft_toLeftOf,0,V80264631c,3502646349,;:;layout_constraintLeft_toLeftOf,0,V803738ddc,3503738e09,;:;layout_constraintLeft_toLeftOf,0,V8049cbc07,35049cbc34,;:;android\:layout_marginTop,0,V801b14677,2f01b1469e,;:;android\:layout_marginTop,0,V80284697f,2f028469a6,;:;android\:layout_marginTop,0,V802e176c8,2f02e176ef,;:;android\:layout_marginTop,0,V803658bbb,2f03658be2,;:;android\:layout_marginTop,0,V8048db9e5,2f048dba0c,;:;constraintSetStart,0,V805baea7f,3c05baeab3,;reference:;layout_constraintCircleAngle,0,V40096191e,3e00961958,;float:;layout_constraintCircleAngle,0,V801bb47ba,3301bb47e5,;:;layout_constraintCircleAngle,0,V8025e6224,33025e624f,;:;layout_constraintCircleAngle,0,V802ea77dc,3302ea7807,;:;layout_constraintCircleAngle,0,V8036c8ce3,33036c8d0e,;:;layout_constraintCircleAngle,0,V80497bb10,330497bb3b,;:;motionTarget,0,V401132fce,3901133003,;reference|string:;motionTarget,0,V802b0725a,2302b07275,;:;motionTarget,0,V80403a769,230403a784,;:;motionTarget,0,V8041baa96,23041baab1,;:;motionTarget,0,V8044fb105,23044fb120,;:;motionTarget,0,V80458b243,230458b25e,;:;motionTarget,0,V80473b5bc,230473b5d7,;:;motionTarget,0,V805e4f171,2305e4f18c,;:;SharedValue,0,V805edf2eb,3305edf316,;integer:;motionEffect_translationY,0,V804f1cc51,4304f1cc8c,;dimension:;limitBoundsTo,0,V8055cdf0a,37055cdf39,;reference:;android\:text,0,V80502cf52,230502cf6d,;:;android\:text,0,V80584e4f0,230584e50b,;:;onStateTransition,0,V401143008,b011a3127,;enum:actionDown:1,actionUp:2,actionDownUp:3,sharedValueSet:4,sharedValueUnset:5,;onStateTransition,0,V805e6f1bf,2805e6f1df,;:;springMass,0,V8054ddc4e,30054ddc76,;float:;layout_constraintRight_toRightOf,0,V400c62203,b00c8227d,;enum|reference:parent:0,;layout_constraintRight_toRightOf,0,V801c44956,3701c44985,;:;layout_constraintRight_toRightOf,0,V8026763c0,37026763ef,;:;layout_constraintRight_toRightOf,0,V803768e80,3703768eaf,;:;layout_constraintRight_toRightOf,0,V8049fbcab,37049fbcda,;:;chainUseRtl,0,V4001b0440,2f001b046b,;boolean:;chainUseRtl,0,V801fc541b,2201fc5435,;:;chainUseRtl,0,V802355c53,2202355c6d,;:;chainUseRtl,0,V8031b80c8,22031b80e2,;:;chainUseRtl,0,V803ad9944,2203ad995e,;:;chainUseRtl,0,V804d6c7c5,2204d6c7df,;:;scaleFromTextSize,0,V8050dd116,3b050dd149,;dimension:;circularflow_radiusInDP,0,V400200557,3a0020058d,;string:;circularflow_radiusInDP,0,V8025560d2,2e025560f8,;:;saturation,0,V803f4a4bc,3003f4a4e4,;float:;nestedScrollFlags,0,V8055ddf42,f0562e049,;flags:none:0,disablePostScroll:1,disableScroll:2,supportScrollUp:4,;placeholder_emptyVisibility,0,V401283335,b012c33e4,;enum:visible:0,invisible:4,gone:8,;placeholder_emptyVisibility,0,V802aa7170,3202aa719a,;:;reactiveGuide_applyToAllConstraintSets,0,V802a6707f,4e02a670c5,;boolean:;layout_constraintCircleRadius,0,V40097195d,430097199c,;dimension:;layout_constraintCircleRadius,0,V801ba4785,3401ba47b1,;:;layout_constraintCircleRadius,0,V8025d61ef,34025d621b,;:;layout_constraintCircleRadius,0,V802e977a7,3402e977d3,;:;layout_constraintCircleRadius,0,V8036b8cae,34036b8cda,;:;layout_constraintCircleRadius,0,V80496badb,340496bb07,;:;moveWhenScrollAtTop,0,V80563e052,3b0563e085,;boolean:;layout_constraintDimensionRatio,0,V4009819a1,42009819df,;string:;layout_constraintDimensionRatio,0,V801e950e3,3601e95111,;:;layout_constraintDimensionRatio,0,V802996dc4,3602996df2,;:;layout_constraintDimensionRatio,0,V8030a7dc3,36030a7df1,;:;layout_constraintDimensionRatio,0,V8039b960d,36039b963b,;:;layout_constraintDimensionRatio,0,V804c5c463,3604c5c491,;:;layout_constraintGuide_percent,0,V400a11b60,4000a11b9c,;float:;layout_constraintGuide_percent,0,V801bf4855,3501bf4882,;:;layout_constraintGuide_percent,0,V8026262bf,35026262ec,;:;layout_constraintGuide_percent,0,V802ee7877,3502ee78a4,;:;layout_constraintGuide_percent,0,V803708d7e,3503708dab,;:;layout_constraintGuide_percent,0,V8049abbaa,35049abbd7,;:;waveOffset,0,V4015a3b3e,36015a3b70,;dimension|float:;waveOffset,0,V80423abb1,210423abca,;:;waveOffset,0,V8045eb318,21045eb331,;:;android\:paddingStart,0,V802425e45,2b02425e68,;:;flow_lastHorizontalBias,0,V400580ea7,3900580edc,;float:;flow_lastHorizontalBias,0,V802125712,2e02125738,;:;flow_lastHorizontalBias,0,V802526075,2e0252609b,;:;flow_lastHorizontalBias,0,V8033183bf,2e033183e5,;:;flow_lastHorizontalBias,0,V803c39c3b,2e03c39c61,;:;path_percent,0,V401233260,2e0123328a,;float:;triggerId,0,V8047db7c3,33047db7ee,;reference:;layout_constraintLeft_creator,0,V400bb2005,4100bb2042,;integer:;layout_constraintLeft_creator,0,V801e44fd4,3401e45000,;:;layout_constraintLeft_creator,0,V802946cb5,3402946ce1,;:;layout_constraintLeft_creator,0,V803057cb4,3403057ce0,;:;layout_constraintLeft_creator,0,V8039694fe,340396952a,;:;layout_constraintLeft_creator,0,V804c0c354,3404c0c380,;:;motionEffect_move,0,V804f4cd14,f04face26,;enum:auto:-1,north:0,south:1,east:2,west:3,;reactiveGuide_applyToConstraintSet,0,V802a770ce,4c02a77112,;reference:;grid_columns,0,V803e3a179,3403e3a1a5,;integer:;layout_constraintBaseline_toBottomOf,0,V40088169a,b008a1718,;enum|reference:parent:0,;layout_constraintBaseline_toBottomOf,0,V801cb4ae1,3b01cb4b14,;:;layout_constraintBaseline_toBottomOf,0,V8026e654b,3b026e657e,;:;layout_constraintBaseline_toBottomOf,0,V8037d900b,3b037d903e,;:;layout_constraintBaseline_toBottomOf,0,V804a6be36,3b04a6be69,;:;clearsTag,0,V805eff351,3305eff37c,;reference:;layout_constrainedWidth,0,V400831593,3b008315ca,;boolean:;layout_constrainedWidth,0,V801d84d63,2e01d84d89,;:;layout_constrainedWidth,0,V802886a44,2e02886a6a,;:;layout_constrainedWidth,0,V802f97a43,2e02f97a69,;:;layout_constrainedWidth,0,V8038a928d,2e038a92b3,;:;layout_constrainedWidth,0,V804b3c0b8,2e04b3c0de,;:;flow_verticalAlign,0,V40066110b,b006b11e4,;enum:top:0,bottom:1,center:2,baseline:3,;flow_verticalAlign,0,V802005495,29020054b6,;:;flow_verticalAlign,0,V802445e9b,2902445ebc,;:;flow_verticalAlign,0,V8031f8142,29031f8163,;:;flow_verticalAlign,0,V803b199be,2903b199df,;:;motionEffect_translationX,0,V804f0cc0d,4304f0cc48,;dimension:;clickAction,0,V8053cd957,f0542da90,;flags:toggle:17,transitionToEnd:1,transitionToStart:16,jumpToEnd:256,jumpToStart:4096,;android\:scaleX,0,V80196438b,25019643a8,;:;android\:scaleX,0,V802c673dc,2502c673f9,;:;android\:scaleX,0,V803548945,2503548962,;:;android\:scaleX,0,V80414a985,250414a9a2,;:;android\:scaleX,0,V8042baceb,25042bad08,;:;android\:scaleX,0,V8046bb484,25046bb4a1,;:;android\:scaleX,0,V805a1e849,2505a1e866,;:;mock_showLabel,0,V804dec946,3604dec974,;boolean:;deltaPolarAngle,0,V4002b0766,31002b0793,;float:;android\:scaleY,0,V8019943bb,25019943d8,;:;android\:scaleY,0,V802c9740c,2502c97429,;:;android\:scaleY,0,V80355896b,2503558988,;:;android\:scaleY,0,V80415a9ab,250415a9c8,;:;android\:scaleY,0,V8042cad11,25042cad2e,;:;android\:scaleY,0,V8046cb4aa,25046cb4c7,;:;android\:scaleY,0,V805a4e879,2505a4e896,;:;layout_constraintHorizontal_chainStyle,0,V400b51eee,b00b91fbb,;enum:spread:0,spread_inside:1,packed:2,;layout_constraintHorizontal_chainStyle,0,V801ec518c,3d01ec51c1,;:;layout_constraintHorizontal_chainStyle,0,V8029c6e6d,3d029c6ea2,;:;layout_constraintHorizontal_chainStyle,0,V8030d7e6c,3d030d7ea1,;:;layout_constraintHorizontal_chainStyle,0,V8039e96b6,3d039e96eb,;:;layout_constraintHorizontal_chainStyle,0,V804c8c50c,3d04c8c541,;:;grid_spans,0,V803e4a1ae,3103e4a1d7,;string:;android\:layout_marginEnd,0,V801b346b0,2f01b346d7,;:;android\:layout_marginEnd,0,V802876a14,2f02876a3b,;:;android\:layout_marginEnd,0,V802e37701,2f02e37728,;:;android\:layout_marginEnd,0,V803668beb,2f03668c12,;:;android\:layout_marginEnd,0,V8048fba16,2f048fba3d,;:;motion_triggerOnCollision,0,V80480b843,430480b87e,;reference:;ifTagNotSet,0,V805f1f3b8,3505f1f3e5,;reference:;flow_firstVerticalStyle,0,V400470c00,b004b0cbe,;enum:spread:0,spread_inside:1,packed:2,;flow_firstVerticalStyle,0,V8020c5626,2e020c564c,;:;flow_firstVerticalStyle,0,V8024c5f89,2e024c5faf,;:;flow_firstVerticalStyle,0,V8032b82d3,2e032b82f9,;:;flow_firstVerticalStyle,0,V803bd9b4f,2e03bd9b75,;:;layout_constraintWidth,0,V400de2644,b00e3275f,;dimension|enum|string:match_parent:-1,wrap_content:-2,match_constraint:-3,wrap_content_constrained:-4,;layout_constraintWidth,0,V801ab45a3,2d01ab45c8,;:;layout_constraintWidth,0,V8027b67cd,2d027b67f2,;:;layout_constraintWidth,0,V802db75f4,2d02db7619,;:;layout_constraintWidth,0,V80486b920,2d0486b945,;:;android\:layout_marginVertical,0,V8028168e7,3402816913,;:;region_heightLessThan,0,V805def065,3f05def09c,;dimension:;flow_lastVerticalBias,0,V4005e0fa5,37005e0fd8,;float:;flow_lastVerticalBias,0,V802135741,2c02135765,;:;flow_lastVerticalBias,0,V8025360a4,2c025360c8,;:;flow_lastVerticalBias,0,V8033283ee,2c03328412,;:;flow_lastVerticalBias,0,V803c49c6a,2c03c49c8e,;:;android\:shadowColor,0,V80588e58e,2a0588e5b0,;:;textureHeight,0,V80514d270,370514d29f,;dimension:;layout_constraintWidth_default,0,V400e42764,b00e82813,;enum:spread:0,wrap:1,percent:2,;layout_constraintWidth_default,0,V801dc4e30,3501dc4e5d,;:;layout_constraintWidth_default,0,V8028c6b11,35028c6b3e,;:;layout_constraintWidth_default,0,V802fd7b10,3502fd7b3d,;:;layout_constraintWidth_default,0,V8038e935a,35038e9387,;:;layout_constraintWidth_default,0,V804b7c185,3504b7c1b2,;:;upDuration,0,V805e8f208,3205e8f232,;integer:;viewTransitionMode,0,V4014f39bb,b01543a80,;enum:currentState:0,allStates:1,noState:2,;viewTransitionMode,0,V805e5f195,2905e5f1b6,;:;setsTag,0,V805eef31f,3105eef348,;reference:;SharedValueId,0,V805ecf2b3,3705ecf2e2,;reference:;android\:layout_margin,0,V8027f6883,2c027f68a7,;:;motionEffect_strict,0,V804fbce2f,3b04fbce62,;boolean:;borderRoundPercent,0,V4001a040b,34001a043b,;float:;borderRoundPercent,0,V8050ed152,29050ed173,;:;borderRoundPercent,0,V80591e709,290591e72a,;:;layout_goneMarginEnd,0,V400f42a43,3a00f42a79,;dimension:;layout_goneMarginEnd,0,V801d54cd9,2b01d54cfc,;:;layout_goneMarginEnd,0,V802786743,2b02786766,;:;layout_goneMarginEnd,0,V802f679b9,2b02f679dc,;:;layout_goneMarginEnd,0,V803879203,2b03879226,;:;layout_goneMarginEnd,0,V804b0c02e,2b04b0c051,;:;android\:shadowRadius,0,V8050ad09e,2b050ad0c1,;:;android\:shadowRadius,0,V8058be609,2b058be62c,;:;region_heightMoreThan,0,V805dff0a5,3f05dff0dc,;dimension:;constraint_referenced_tags,0,V400240641,3d0024067a,;string:;constraint_referenced_tags,0,V801f55316,3101f5533f,;:;constraint_referenced_tags,0,V802345c21,3102345c4a,;:;constraint_referenced_tags,0,V803a6981b,3103a69844,;:;constraint_referenced_tags,0,V804d0c695,3104d0c6be,;:;methodName,0,V803d69ece,3103d69ef7,;string:;circularflow_defaultRadius,0,V4001f0516,40001f0552,;dimension:;circularflow_defaultRadius,0,V80258615b,3102586184,;:;transformPivotTarget,0,V401463825,3a0146385b,;reference:;transformPivotTarget,0,V8019e4463,2b019e4486,;:;transformPivotTarget,0,V802ce74b4,2b02ce74d7,;:;transformPivotTarget,0,V80411a92c,2b0411a94f,;:;transformPivotTarget,0,V805ace912,2b05ace935,;:;layout_constraintCircle,0,V4009518e0,3d00951919,;reference:;layout_constraintCircle,0,V801b94756,2e01b9477c,;:;layout_constraintCircle,0,V8025c61c0,2e025c61e6,;:;layout_constraintCircle,0,V8036a8c7f,2e036a8ca5,;:;layout_constraintCircle,0,V80495baac,2e0495bad2,;:;customIntegerValue,0,V803d99f76,3a03d99fa8,;integer:;android\:alpha,0,V801874291,24018742ad,;:;android\:alpha,0,V802b772e2,2402b772fe,;:;android\:alpha,0,V8034f887d,24034f8899,;:;android\:alpha,0,V8040aa806,24040aa822,;:;android\:alpha,0,V80426ac23,240426ac3f,;:;android\:alpha,0,V80465b390,240465b3ac,;:;android\:alpha,0,V80576e31d,240576e339,;:;crossfade,0,V803f8a57a,2f03f8a5a1,;float:;flow_verticalStyle,0,V4006e1254,b0072130d,;enum:spread:0,spread_inside:1,packed:2,;flow_verticalStyle,0,V801ff546b,2901ff548c,;:;flow_verticalStyle,0,V802385ca3,2902385cc4,;:;flow_verticalStyle,0,V8031e8118,29031e8139,;:;flow_verticalStyle,0,V803b09994,2903b099b5,;:;telltales_velocityMode,0,V80531d774,f0536d87a,;enum:layout:0,postLayout:1,staticPostLayout:2,staticLayout:3,;android\:paddingRight,0,V802415e19,2b02415e3c,;:;flow_horizontalGap,0,V400520dae,3800520de2,;dimension:;flow_horizontalGap,0,V802065590,29020655b1,;:;flow_horizontalGap,0,V8023b5d1e,29023b5d3f,;:;flow_horizontalGap,0,V80325823d,290325825e,;:;flow_horizontalGap,0,V803b79ab9,2903b79ada,;:;stateLabels,0,V8034486b6,32034486e0,;string:;round,0,V803f9a5aa,2f03f9a5d1,;dimension:;carousel_touchUpMode,0,V801743ffc,f017740a2,;enum:immediateStop:1,carryVelocity:2,;autoCompleteMode,0,V80547db3d,f054adbdd,;enum:continuousVelocity:0,spring:1,;textBackgroundRotate,0,V80519d38d,3a0519d3bf,;float:;motionProgress,0,V401112f6d,3001112f99,;float:;motionProgress,0,V8021c5884,25021c58a1,;:;motionProgress,0,V8033b8531,25033b854e,;:;motionProgress,0,V803d09dfe,2503d09e1b,;:;motionProgress,0,V80407a7d6,250407a7f3,;:;motionProgress,0,V8041fab27,25041fab44,;:;motionProgress,0,V8045db2f2,25045db30f,;:;motionProgress,0,V80522d504,250522d521,;:;motionProgress,0,V80579e34c,250579e369,;:;flow_horizontalAlign,0,V4004c0cc3,b00500d73,;enum:start:0,end:1,center:2,;flow_horizontalAlign,0,V8020154bf,2b020154e2,;:;flow_horizontalAlign,0,V802455ec5,2b02455ee8,;:;flow_horizontalAlign,0,V80320816c,2b0320818f,;:;flow_horizontalAlign,0,V803b299e8,2b03b29a0b,;:;grid_orientation,0,V803e8a283,f03eba31d,;enum:horizontal:0,vertical:1,;viewTransitionOnCross,0,V8047ab747,3f047ab77e,;reference:;+id:view_transition,0,V4016b3ddc,2c016b3e04,;"";motion_base,0,V4016a3db3,28016a3dd7,;"";+styleable:KeyFramesAcceleration,0,V40434ae12,180436ae5c,;;ImageFilterView,0,V403f1a425,180400a70f,;-blendSrc:reference:-altSrc:reference:-saturation:float:-brightness:float:-warmth:float:-contrast:float:-crossfade:float:-round:dimension:-overlay:boolean:-roundPercent:float:-imagePanX:float:-imagePanY:float:-imageZoom:float:-imageRotate:float:;Constraint,0,V4017e41b2,1802225993,;-orientation::-id::-visibility::-visibilityMode::-alpha::-elevation::-rotation::-rotationX::-rotationY::-scaleX::-scaleY::-pivotAnchor::-transformPivotX::-transformPivotY::-transformPivotTarget::-translationX::-translationY::-translationZ::-layout_width::-layout_height::-layout_constraintWidth::-layout_constraintHeight::-layout_marginStart::-layout_marginBottom::-layout_marginTop::-layout_marginEnd::-layout_marginLeft::-layout_marginRight::-layout_constraintCircle::-layout_constraintCircleRadius::-layout_constraintCircleAngle::-layout_constraintGuide_begin::-layout_constraintGuide_end::-layout_constraintGuide_percent::-guidelineUseRtl::-layout_constraintLeft_toLeftOf::-layout_constraintLeft_toRightOf::-layout_constraintRight_toLeftOf::-layout_constraintRight_toRightOf::-layout_constraintTop_toTopOf::-layout_constraintTop_toBottomOf::-layout_constraintBottom_toTopOf::-layout_constraintBottom_toBottomOf::-layout_constraintBaseline_toBaselineOf::-layout_constraintBaseline_toTopOf::-layout_constraintBaseline_toBottomOf::-layout_constraintStart_toEndOf::-layout_constraintStart_toStartOf::-layout_constraintEnd_toStartOf::-layout_constraintEnd_toEndOf::-layout_goneMarginLeft::-layout_goneMarginTop::-layout_goneMarginRight::-layout_goneMarginBottom::-layout_goneMarginStart::-layout_goneMarginEnd::-layout_goneMarginBaseline::-layout_marginBaseline::-layout_constrainedWidth::-layout_constrainedHeight::-layout_constraintHorizontal_bias::-layout_constraintVertical_bias::-layout_constraintWidth_default::-layout_constraintHeight_default::-layout_constraintWidth_min::-layout_constraintWidth_max::-layout_constraintWidth_percent::-layout_constraintHeight_min::-layout_constraintHeight_max::-layout_constraintHeight_percent::-layout_constraintLeft_creator::-layout_constraintTop_creator::-layout_constraintRight_creator::-layout_constraintBottom_creator::-layout_constraintBaseline_creator::-layout_constraintDimensionRatio::-layout_constraintHorizontal_weight::-layout_constraintVertical_weight::-layout_constraintHorizontal_chainStyle::-layout_constraintVertical_chainStyle::-layout_editor_absoluteX::-layout_editor_absoluteY::-layout_wrapBehaviorInParent::-barrierDirection::-barrierMargin::-constraint_referenced_ids::-constraint_referenced_tags::-maxHeight::-maxWidth::-minHeight::-minWidth::-barrierAllowsGoneWidgets::-chainUseRtl::-flow_horizontalStyle::-flow_verticalStyle::-flow_verticalAlign::-flow_horizontalAlign::-flow_verticalBias::-flow_horizontalBias::-flow_wrapMode::-flow_maxElementsWrap::-flow_horizontalGap::-flow_verticalGap::-flow_firstHorizontalStyle::-flow_firstVerticalStyle::-flow_firstHorizontalBias::-flow_firstVerticalBias::-flow_lastHorizontalStyle::-flow_lastVerticalStyle::-flow_lastHorizontalBias::-flow_lastVerticalBias::-animateRelativeTo::-animateCircleAngleTo::-transitionEasing::-pathMotionArc::-polarRelativeTo::-transitionPathRotate::-drawPath::-motionProgress::-layout_constraintTag::-motionStagger::-quantizeMotionSteps::-quantizeMotionPhase::-quantizeMotionInterpolator::;Carousel,0,V4016c3e09,18017d41ad,;-carousel_firstView:reference:-carousel_previousState:reference:-carousel_nextState:reference:-carousel_infinite:boolean:-carousel_forwardTransition:reference:-carousel_backwardTransition:reference:-carousel_touchUp_dampeningFactor:float:-carousel_touchUpMode:enum:immediateStop:1,carryVelocity:2,-carousel_touchUp_velocityThreshold:float:-carousel_emptyViewsBehavior:enum:invisible:4,gone:8,;ConstraintLayout_ReactiveGuide,0,V402a36fb9,1802a8712b,;-reactiveGuide_valueId:reference:-reactiveGuide_animateChange:boolean:-reactiveGuide_applyToAllConstraintSets:boolean:-reactiveGuide_applyToConstraintSet:reference:;CustomAttribute,0,V403d49e66,1803e0a11a,;-attributeName:string:-methodName:string:-customColorValue:color:-customColorDrawableValue:color:-customIntegerValue:integer:-customFloatValue:float:-customStringValue:string:-customDimension:dimension:-customPixelDimension:dimension:-customBoolean:boolean:-customReference:reference:;Grid,0,V403e1a11f,1803f0a420,;-grid_rows:integer:-grid_columns:integer:-grid_spans:string:-grid_skips:string:-grid_rowWeights:string:-grid_columnWeights:string:-grid_orientation:enum:horizontal:0,vertical:1,-grid_horizontalGaps:dimension:-grid_verticalGaps:dimension:-grid_validateInputs:boolean:-grid_useRtl:boolean:;KeyTrigger,0,V40471b569,180481b897,;-framePosition::-motionTarget::-triggerReceiver:reference:-onNegativeCross:string:-onPositiveCross:string:-onCross:string:-viewTransitionOnNegativeCross:reference:-viewTransitionOnPositiveCross:reference:-viewTransitionOnCross:reference:-triggerSlack:float:-triggerId:reference:-motion_postLayoutCollision:boolean:-motion_triggerOnCollision:reference:;ConstraintLayout_placeholder,0,V402a97130,1802ac71d2,;-placeholder_emptyVisibility::-content::;ViewTransition,0,V405e2f11d,1805f2f3fe,;-id::-motionTarget::-viewTransitionMode::-onStateTransition::-duration::-upDuration:integer:-transitionDisable::-pathMotionArc::-motionInterpolator::-SharedValueId:reference:-SharedValue:integer:-setsTag:reference:-clearsTag:reference:-ifTagSet:reference:-ifTagNotSet:reference:;MotionEffect,0,V404edcb69,1804fcce7b,;-motionEffect_start:integer:-motionEffect_end:integer:-motionEffect_translationX:dimension:-motionEffect_translationY:dimension:-motionEffect_alpha:float:-motionEffect_viewTransition:reference:-motionEffect_move:enum:auto:-1,north:0,south:1,east:2,west:3,-motionEffect_strict:boolean:;KeyTimeCycle,0,V40456b1ee,180470b564,;-framePosition::-motionTarget::-transitionEasing::-curveFit::-waveShape::-wavePeriod::-motionProgress::-waveOffset::-wavePhase::-waveDecay::-alpha::-elevation::-rotation::-rotationX::-rotationY::-transitionPathRotate::-scaleX::-scaleY::-translationX::-translationY::-translationZ::;KeyCycle,0,V4041aaa6a,180430adcb,;-motionTarget::-curveFit::-framePosition::-transitionEasing::-motionProgress::-waveShape::-wavePhase::-wavePeriod::-waveOffset::-waveVariesBy::-transitionPathRotate::-alpha::-elevation::-rotation::-rotationX::-rotationY::-scaleX::-scaleY::-translationX::-translationY::-translationZ::;MotionLabel,0,V40501cf23,18051ed46f,;-text::-textColor::-textSize::-fontFamily::-gravity::-textPanX:float:-textPanY:float:-typeface::-shadowRadius::-textStyle::-borderRound::-scaleFromTextSize:dimension:-borderRoundPercent::-textOutlineThickness::-textOutlineColor::-textBackground:reference:-autoSizeTextType::-textureWidth:dimension:-textureHeight:dimension:-textureBlurFactor:integer:-textBackgroundPanX:float:-textBackgroundPanY:float:-textBackgroundZoom:float:-textBackgroundRotate:float:-textureEffect:enum:none:0,frost:1,;StateSet,0,V40580e449,180582e4bc,;-defaultState:reference:;MotionScene,0,V4052cd6a8,18052fd73c,;-defaultDuration::-layoutDuringTransition::;MotionLayout,0,V4051fd474,18052bd6a3,;-layoutDescription::-currentState:reference:-motionProgress::-applyMotionScene:boolean:-showPaths:boolean:-motionDebug:enum:NO_DEBUG:0,SHOW_PROGRESS:1,SHOW_PATH:2,SHOW_ALL:3,;Motion,0,V404e0c992,1804eccb64,;-animateRelativeTo::-animateCircleAngleTo::-transitionEasing::-pathMotionArc::-motionPathRotate::-motionStagger::-drawPath::-quantizeMotionSteps::-quantizeMotionPhase::-quantizeMotionInterpolator::;include,0,V405f3f403,1805f5f463,;-constraintSet::;ConstraintOverride,0,V402ad71d7,1803418640,;-orientation::-id::-motionTarget::-visibility::-visibilityMode::-alpha::-elevation::-rotation::-rotationX::-rotationY::-scaleX::-scaleY::-pivotAnchor::-transformPivotX::-transformPivotY::-transformPivotTarget::-translationX::-translationY::-translationZ::-layout_width::-layout_height::-layout_constraintWidth::-layout_constraintHeight::-layout_marginStart::-layout_marginBottom::-layout_marginTop::-layout_marginEnd::-layout_marginLeft::-layout_marginRight::-layout_constraintCircleRadius::-layout_constraintCircleAngle::-layout_constraintGuide_begin::-layout_constraintGuide_end::-layout_constraintGuide_percent::-guidelineUseRtl::-layout_goneMarginLeft::-layout_goneMarginTop::-layout_goneMarginRight::-layout_goneMarginBottom::-layout_goneMarginStart::-layout_goneMarginEnd::-layout_goneMarginBaseline::-layout_marginBaseline::-layout_constrainedWidth::-layout_constrainedHeight::-layout_constraintHorizontal_bias::-layout_constraintVertical_bias::-layout_constraintWidth_default::-layout_constraintHeight_default::-layout_constraintWidth_min::-layout_constraintWidth_max::-layout_constraintWidth_percent::-layout_constraintHeight_min::-layout_constraintHeight_max::-layout_constraintHeight_percent::-layout_constraintLeft_creator::-layout_constraintTop_creator::-layout_constraintRight_creator::-layout_constraintBottom_creator::-layout_constraintBaseline_creator::-layout_constraintDimensionRatio::-layout_constraintHorizontal_weight::-layout_constraintVertical_weight::-layout_constraintHorizontal_chainStyle::-layout_constraintVertical_chainStyle::-layout_editor_absoluteX::-layout_editor_absoluteY::-layout_wrapBehaviorInParent::-barrierDirection::-barrierMargin::-constraint_referenced_ids::-maxHeight::-maxWidth::-minHeight::-minWidth::-barrierAllowsGoneWidgets::-chainUseRtl::-flow_horizontalStyle::-flow_verticalStyle::-flow_verticalAlign::-flow_horizontalAlign::-flow_verticalBias::-flow_horizontalBias::-flow_wrapMode::-flow_maxElementsWrap::-flow_horizontalGap::-flow_verticalGap::-flow_firstHorizontalStyle::-flow_firstVerticalStyle::-flow_firstHorizontalBias::-flow_firstVerticalBias::-flow_lastHorizontalStyle::-flow_lastVerticalStyle::-flow_lastHorizontalBias::-flow_lastVerticalBias::-animateRelativeTo::-animateCircleAngleTo::-transitionEasing::-pathMotionArc::-polarRelativeTo::-transitionPathRotate::-drawPath::-motionProgress::-layout_constraintTag::-motionStagger::-quantizeMotionSteps::-quantizeMotionPhase::-quantizeMotionInterpolator::;MotionTelltales,0,V40530d741,180539d907,;-telltales_velocityMode:enum:layout:0,postLayout:1,staticPostLayout:2,staticLayout:3,-telltales_tailColor:color:-telltales_tailScale:float:;MockView,0,V404d8c7fd,1804dfc98d,;-mock_label:string:-mock_labelColor:color:-mock_labelBackgroundColor:color:-mock_diagonalsColor:color:-mock_showDiagonals:boolean:-mock_showLabel:boolean:;KeyAttribute,0,V40401a714,180419aa65,;-framePosition::-motionTarget::-transitionEasing::-curveFit::-motionProgress::-alpha::-elevation::-rotation::-rotationX::-rotationY::-transformPivotX::-transformPivotY::-transformPivotTarget::-transitionPathRotate::-scaleX::-scaleY::-translationX::-translationY::-translationZ::;ConstraintSet,0,V403428645,1803d39e61,;-deriveConstraintsFrom:reference:-stateLabels:string:-constraintRotate:enum:none:0,right:1,left:2,x_right:3,x_left:4,-orientation::-id::-visibility::-alpha::-elevation::-rotation::-rotationX::-rotationY::-scaleX::-scaleY::-pivotAnchor::-pivotX::-pivotY::-transformPivotX::-transformPivotY::-translationX::-translationY::-translationZ::-layout_width::-layout_height::-layout_marginStart::-layout_marginBottom::-layout_marginTop::-layout_marginEnd::-layout_marginLeft::-layout_marginRight::-layout_constraintCircle::-layout_constraintCircleRadius::-layout_constraintCircleAngle::-layout_constraintGuide_begin::-layout_constraintGuide_end::-layout_constraintGuide_percent::-guidelineUseRtl::-layout_constraintLeft_toLeftOf::-layout_constraintLeft_toRightOf::-layout_constraintRight_toLeftOf::-layout_constraintRight_toRightOf::-layout_constraintTop_toTopOf::-layout_constraintTop_toBottomOf::-layout_constraintBottom_toTopOf::-layout_constraintBottom_toBottomOf::-layout_constraintBaseline_toBaselineOf::-layout_constraintBaseline_toTopOf::-layout_constraintBaseline_toBottomOf::-layout_constraintStart_toEndOf::-layout_constraintStart_toStartOf::-layout_constraintEnd_toStartOf::-layout_constraintEnd_toEndOf::-layout_goneMarginLeft::-layout_goneMarginTop::-layout_goneMarginRight::-layout_goneMarginBottom::-layout_goneMarginStart::-layout_goneMarginEnd::-layout_goneMarginBaseline::-layout_marginBaseline::-layout_constrainedWidth::-layout_constrainedHeight::-layout_constraintHorizontal_bias::-layout_constraintVertical_bias::-layout_constraintWidth_default::-layout_constraintHeight_default::-layout_constraintWidth_min::-layout_constraintWidth_max::-layout_constraintWidth_percent::-layout_constraintHeight_min::-layout_constraintHeight_max::-layout_constraintHeight_percent::-layout_constraintLeft_creator::-layout_constraintTop_creator::-layout_constraintRight_creator::-layout_constraintBottom_creator::-layout_constraintBaseline_creator::-layout_constraintDimensionRatio::-layout_constraintHorizontal_weight::-layout_constraintVertical_weight::-layout_constraintHorizontal_chainStyle::-layout_constraintVertical_chainStyle::-layout_editor_absoluteX::-layout_editor_absoluteY::-layout_wrapBehaviorInParent::-barrierDirection::-constraint_referenced_ids::-constraint_referenced_tags::-barrierMargin::-maxHeight::-maxWidth::-minHeight::-minWidth::-barrierAllowsGoneWidgets::-chainUseRtl::-flow_horizontalStyle::-flow_verticalStyle::-flow_verticalAlign::-flow_horizontalAlign::-flow_verticalBias::-flow_horizontalBias::-flow_wrapMode::-flow_maxElementsWrap::-flow_horizontalGap::-flow_verticalGap::-flow_firstHorizontalStyle::-flow_firstVerticalStyle::-flow_firstHorizontalBias::-flow_firstVerticalBias::-flow_lastHorizontalStyle::-flow_lastVerticalStyle::-flow_lastHorizontalBias::-flow_lastVerticalBias::-animateRelativeTo::-animateCircleAngleTo::-transitionEasing::-pathMotionArc::-polarRelativeTo::-transitionPathRotate::-motionStagger::-quantizeMotionSteps::-drawPath::-motionProgress::-layout_constraintTag::;OnClick,0,V4053ad90c,180543daa9,;-targetId::-clickAction:flags:toggle:17,transitionToEnd:1,transitionToStart:16,jumpToEnd:256,jumpToStart:4096,;ConstraintLayout_Layout,0,V402235998,1802a26fb4,;-orientation::-minWidth::-minHeight::-maxWidth::-maxHeight::-visibility::-elevation::-layout_optimizationLevel::-layoutDescription::-constraintSet::-barrierDirection::-barrierAllowsGoneWidgets::-barrierMargin::-constraint_referenced_ids::-constraint_referenced_tags::-chainUseRtl::-flow_horizontalStyle::-flow_verticalStyle::-flow_wrapMode::-flow_maxElementsWrap::-flow_horizontalGap::-flow_verticalGap::-padding::-paddingTop::-paddingBottom::-paddingLeft::-paddingRight::-paddingStart::-paddingEnd::-flow_verticalAlign::-flow_horizontalAlign::-flow_verticalBias::-flow_horizontalBias::-flow_firstHorizontalStyle::-flow_firstVerticalStyle::-flow_firstHorizontalBias::-flow_firstVerticalBias::-flow_lastHorizontalStyle::-flow_lastVerticalStyle::-flow_lastHorizontalBias::-flow_lastVerticalBias::-circularflow_radiusInDP::-circularflow_angles::-circularflow_viewCenter::-circularflow_defaultRadius::-circularflow_defaultAngle::-layout_constraintCircle::-layout_constraintCircleRadius::-layout_constraintCircleAngle::-layout_constraintGuide_begin::-layout_constraintGuide_end::-layout_constraintGuide_percent::-guidelineUseRtl::-layout_constraintLeft_toLeftOf::-layout_constraintLeft_toRightOf::-layout_constraintRight_toLeftOf::-layout_constraintRight_toRightOf::-layout_constraintTop_toTopOf::-layout_constraintTop_toBottomOf::-layout_constraintBottom_toTopOf::-layout_constraintBottom_toBottomOf::-layout_constraintBaseline_toBaselineOf::-layout_constraintBaseline_toTopOf::-layout_constraintBaseline_toBottomOf::-layout_constraintStart_toEndOf::-layout_constraintStart_toStartOf::-layout_constraintEnd_toStartOf::-layout_constraintEnd_toEndOf::-layout_goneMarginLeft::-layout_goneMarginTop::-layout_goneMarginRight::-layout_goneMarginBottom::-layout_goneMarginStart::-layout_goneMarginEnd::-layout_goneMarginBaseline::-layout_marginBaseline::-layout_constraintWidth::-layout_constraintHeight::-layout_width::-layout_height::-layout_margin::-layout_marginHorizontal::-layout_marginVertical::-layout_marginLeft::-layout_marginRight::-layout_marginTop::-layout_marginBottom::-layout_marginStart::-layout_marginEnd::-layout_constrainedWidth::-layout_constrainedHeight::-layout_constraintHorizontal_bias::-layout_constraintVertical_bias::-layout_constraintWidth_default::-layout_constraintHeight_default::-layout_constraintWidth_min::-layout_constraintWidth_max::-layout_constraintWidth_percent::-layout_constraintHeight_min::-layout_constraintHeight_max::-layout_constraintHeight_percent::-layout_constraintLeft_creator::-layout_constraintTop_creator::-layout_constraintRight_creator::-layout_constraintBottom_creator::-layout_constraintBaseline_creator::-layout_constraintDimensionRatio::-layout_constraintHorizontal_weight::-layout_constraintVertical_weight::-layout_constraintHorizontal_chainStyle::-layout_constraintVertical_chainStyle::-layout_editor_absoluteX::-layout_editor_absoluteY::-layout_constraintTag::-layout_wrapBehaviorInParent::;Variant,0,V405dbefbc,1805e1f118,;-region_widthLessThan:dimension:-region_widthMoreThan:dimension:-region_heightLessThan:dimension:-region_heightMoreThan:dimension:-constraints::;OnSwipe,0,V40544daae,18056fe286,;-dragScale:float:-dragThreshold:float:-autoCompleteMode:enum:continuousVelocity:0,spring:1,-maxVelocity:float:-maxAcceleration:float:-springMass:float:-springStiffness:float:-springDamping:float:-springStopThreshold:float:-springBoundary:flags:overshoot:0,bounceStart:1,bounceEnd:2,bounceBoth:3,-dragDirection::-touchAnchorId::-touchAnchorSide::-rotationCenterId:reference:-touchRegionId:reference:-limitBoundsTo:reference:-nestedScrollFlags:flags:none:0,disablePostScroll:1,disableScroll:2,supportScrollUp:4,-moveWhenScrollAtTop:boolean:-onTouchUp:enum:autoComplete:0,autoCompleteToStart:1,autoCompleteToEnd:2,stop:3,decelerate:4,decelerateAndComplete:5,neverCompleteToStart:6,neverCompleteToEnd:7,;Layout,0,V40482b89c,1804d7c7f8,;-layout_width::-layout_height::-layout_constraintWidth::-layout_constraintHeight::-layout_marginStart::-layout_marginBottom::-layout_marginTop::-layout_marginEnd::-layout_marginLeft::-layout_marginRight::-layout_constraintCircle::-layout_constraintCircleRadius::-layout_constraintCircleAngle::-layout_constraintGuide_begin::-layout_constraintGuide_end::-layout_constraintGuide_percent::-guidelineUseRtl::-layout_constraintLeft_toLeftOf::-layout_constraintLeft_toRightOf::-layout_constraintRight_toLeftOf::-layout_constraintRight_toRightOf::-layout_constraintTop_toTopOf::-layout_constraintTop_toBottomOf::-layout_constraintBottom_toTopOf::-layout_constraintBottom_toBottomOf::-layout_constraintBaseline_toBaselineOf::-layout_constraintBaseline_toTopOf::-layout_constraintBaseline_toBottomOf::-layout_constraintStart_toEndOf::-layout_constraintStart_toStartOf::-layout_constraintEnd_toStartOf::-layout_constraintEnd_toEndOf::-layout_goneMarginLeft::-layout_goneMarginTop::-layout_goneMarginRight::-layout_goneMarginBottom::-layout_goneMarginStart::-layout_goneMarginEnd::-layout_goneMarginBaseline::-layout_marginBaseline::-layout_constrainedWidth::-layout_constrainedHeight::-layout_constraintHorizontal_bias::-layout_constraintVertical_bias::-layout_constraintWidth_default::-layout_constraintHeight_default::-layout_constraintWidth_min::-layout_constraintWidth_max::-orientation::-layout_constraintWidth_percent::-layout_constraintHeight_min::-layout_constraintHeight_max::-layout_constraintHeight_percent::-layout_constraintLeft_creator::-layout_constraintTop_creator::-layout_constraintRight_creator::-layout_constraintBottom_creator::-layout_constraintBaseline_creator::-layout_constraintDimensionRatio::-layout_constraintHorizontal_weight::-layout_constraintVertical_weight::-layout_constraintHorizontal_chainStyle::-layout_constraintVertical_chainStyle::-layout_editor_absoluteX::-layout_editor_absoluteY::-layout_wrapBehaviorInParent::-barrierDirection::-barrierMargin::-constraint_referenced_ids::-constraint_referenced_tags::-maxHeight:dimension:-maxWidth:dimension:-minHeight:dimension:-minWidth:dimension:-barrierAllowsGoneWidgets::-chainUseRtl::;State,0,V4057ce3b3,18057fe444,;-id::-constraints:reference:;KeyPosition,0,V4043aaeac,180455b1e9,;-keyPositionType:enum:deltaRelative:0,pathRelative:1,parentRelative:2,axisRelative:3,-percentX:float:-percentY:float:-percentWidth:float:-percentHeight:float:-framePosition::-motionTarget::-transitionEasing::-pathMotionArc::-curveFit::-drawPath::-sizePercent::;Transition,0,V405b8ea2f,1805daefb7,;-id::-constraintSetStart:reference:-constraintSetEnd:reference:-transitionDisable::-layoutDuringTransition::-pathMotionArc::-autoTransition:enum:none:0,jumpToStart:1,jumpToEnd:2,animateToStart:3,animateToEnd:4,-motionInterpolator:enum|reference|string:easeInOut:0,easeIn:1,easeOut:2,linear:3,bounce:4,overshoot:5,anticipate:6,-duration::-staggered:float:-transitionFlags:flags:none:0,beginOnFirstDraw:1,disableIntraAutoTransition:2,onInterceptTouchReturnSwipe:4,;KeyFrame,0,V40431add0,180433ae0d,;;KeyFramesVelocity,0,V40437ae61,180439aea7,;;PropertySet,0,V40570e28b,18057be3ae,;-visibility::-visibilityMode::-alpha::-motionProgress::-layout_constraintTag::;MotionHelper,0,V404fdce80,180500cf1e,;-onShow:boolean:-onHide:boolean:;TextEffects,0,V40583e4c1,180592e743,;-text::-textSize::-fontFamily::-typeface::-shadowColor::-shadowDx::-shadowDy::-shadowRadius::-textStyle::-textFillColor:color:-textOutlineThickness::-textOutlineColor::-borderRound::-borderRoundPercent::;Transform,0,V40593e748,1805b7ea2a,;-elevation::-rotation::-rotationX::-rotationY::-scaleX::-scaleY::-transformPivotX::-transformPivotY::-transformPivotTarget::-translationX:dimension:-translationY:dimension:-translationZ:dimension:;