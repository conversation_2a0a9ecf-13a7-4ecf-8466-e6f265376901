<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#007AFF</color>
    <color name="background">#FFFFFF</color>
    <color name="background_card">#FFFFFF</color>
    <color name="background_light">#F8F9FA</color>
    <color name="background_primary">#FFFFFF</color>
    <color name="background_secondary">#F2F2F7</color>
    <color name="background_tertiary">#FFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="border_light">#F2F2F7</color>
    <color name="card_background">#FFFFFF</color>
    <color name="card_pressed">#4DB5DFFF</color>
    <color name="card_selected">#33B5DFFF</color>
    <color name="card_stroke">#B5DFFF</color>
    <color name="dark_gray">#666666</color>
    <color name="divider">#E5E5EA</color>
    <color name="error">#FF3B30</color>
    <color name="gray">#999999</color>
    <color name="green">#34C759</color>
    <color name="info">#007AFF</color>
    <color name="ios_blue">#007AFF</color>
    <color name="ios_blue_dark">#0056CC</color>
    <color name="ios_blue_light">#B5DFFF</color>
    <color name="ios_gray3">#C7C7CC</color>
    <color name="ios_gray5">#E5E5EA</color>
    <color name="ios_gray6">#F2F2F7</color>
    <color name="ios_green">#34C759</color>
    <color name="ios_green_light">#E8F8F5</color>
    <color name="ios_orange">#FF9500</color>
    <color name="ios_orange_light">#FFF8DC</color>
    <color name="ios_pink">#FF2D92</color>
    <color name="ios_purple">#AF52DE</color>
    <color name="ios_purple_light">#F0F0FF</color>
    <color name="ios_red">#FF3B30</color>
    <color name="ios_red_light">#FFE4E6</color>
    <color name="ios_yellow">#FFCC00</color>
    <color name="light_gray">#E0E0E0</color>
    <color name="modal_background">#F2F2F7</color>
    <color name="modal_handle">#C7C7CC</color>
    <color name="orange">#FF9500</color>
    <color name="pastel_blue">#E3F2FD</color>
    <color name="pastel_lavender">#F0F0FF</color>
    <color name="pastel_mint">#E8F8F5</color>
    <color name="pastel_peach">#FFEEE6</color>
    <color name="pastel_pink">#FFE4E6</color>
    <color name="pastel_sky">#E8F6FD</color>
    <color name="pastel_yellow">#FFF8DC</color>
    <color name="primary">#B5DFFF</color>
    <color name="primary_color">#B5DFFF</color>
    <color name="purple_500">#AF52DE</color>
    <color name="purple_700">#8E44AD</color>
    <color name="red">#FF3B30</color>
    <color name="route_accent">@color/info</color>
    <color name="route_card_background">@color/card_background</color>
    <color name="route_card_selected">@color/card_selected</color>
    <color name="route_success">@color/success</color>
    <color name="route_text_primary">@color/text_primary</color>
    <color name="route_text_secondary">@color/text_secondary</color>
    <color name="route_warning">@color/warning</color>
    <color name="shadow_light">#1A000000</color>
    <color name="sky_blue">#B5DFFF</color>
    <color name="sky_blue_accent">#5BB6D6</color>
    <color name="sky_blue_dark">#007AFF</color>
    <color name="sky_blue_light">#E8F6FD</color>
    <color name="sky_blue_primary">#B5DFFF</color>
    <color name="success">#34C759</color>
    <color name="surface">#FFFFFF</color>
    <color name="text_hint">#C7C7CC</color>
    <color name="text_input">#000000</color>
    <color name="text_input_hint">#8E8E93</color>
    <color name="text_on_primary">#FFFFFF</color>
    <color name="text_primary">#1C1C1E</color>
    <color name="text_secondary">#3A3A3C</color>
    <color name="text_tertiary">#8E8E93</color>
    <color name="warning">#FF9500</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="card_corner_radius">20dp</dimen>
    <dimen name="card_elevation">2dp</dimen>
    <dimen name="card_elevation_pressed">6dp</dimen>
    <dimen name="card_elevation_selected">4dp</dimen>
    <dimen name="card_height">72dp</dimen>
    <dimen name="card_margin">12dp</dimen>
    <dimen name="card_padding">16dp</dimen>
    <dimen name="card_spacing">8dp</dimen>
    <dimen name="modal_corner_radius">16dp</dimen>
    <dimen name="modal_handle_height">4dp</dimen>
    <dimen name="modal_handle_margin">8dp</dimen>
    <dimen name="modal_handle_width">36dp</dimen>
    <dimen name="route_button_corner_radius">12dp</dimen>
    <dimen name="route_button_height">48dp</dimen>
    <dimen name="route_card_corner_radius">@dimen/card_corner_radius</dimen>
    <dimen name="route_card_elevation">@dimen/card_elevation</dimen>
    <dimen name="route_card_margin">@dimen/card_margin</dimen>
    <dimen name="route_card_padding">@dimen/card_padding</dimen>
    <dimen name="route_card_spacing">@dimen/card_spacing</dimen>
    <dimen name="route_checkbox_size">20dp</dimen>
    <dimen name="route_content_spacing">8dp</dimen>
    <dimen name="route_detail_text_size">12sp</dimen>
    <dimen name="route_icon_size">24dp</dimen>
    <dimen name="route_icon_small">20dp</dimen>
    <dimen name="route_item_spacing">12dp</dimen>
    <dimen name="route_section_spacing">16dp</dimen>
    <dimen name="route_subtitle_text_size">14sp</dimen>
    <dimen name="route_time_text_size">16sp</dimen>
    <dimen name="route_title_text_size">18sp</dimen>
    <string name="app_name">TimeMate</string>
    <style name="Base.Theme.TimeMate" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="BottomSheetDialogTheme" parent="Theme.Material3.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
    <style name="BottomSheetShapeAppearance" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">@dimen/modal_corner_radius</item>
        <item name="cornerSizeTopRight">@dimen/modal_corner_radius</item>
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="BottomSheetStyle" parent="Widget.Material3.BottomSheet">
        <item name="shapeAppearanceOverlay">@style/BottomSheetShapeAppearance</item>
        <item name="behavior_peekHeight">400dp</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_skipCollapsed">false</item>
        <item name="android:elevation">8dp</item>
    </style>
    <style name="CalendarDateTextAppearance">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
    <style name="CalendarHeaderTextAppearance">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
    <style name="CalendarWeekDayTextAppearance">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    <style name="Theme.TimeMate" parent="Base.Theme.TimeMate"/>
    <style name="TimeMateAppBar">
        <item name="android:background">@color/sky_blue_primary</item>
        <item name="android:elevation">0dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
    </style>
    <style name="TimeMateBottomNavigation">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">8dp</item>
    </style>
    <style name="TimeMateButton">
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">@font/pretendard_medium</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:elevation">1dp</item>
        <item name="android:background">@drawable/timemate_button_background</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="TimeMateButtonOutlined">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:background">@drawable/timemate_button_outlined</item>
        <item name="android:textColor">@color/sky_blue_accent</item>
    </style>
    <style name="TimeMateButtonPrimary">
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">@font/pretendard_bold</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:background">@drawable/timemate_button_primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:elevation">1dp</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="TimeMateCard">
        <item name="android:background">@drawable/ios_card_background</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:elevation">4dp</item>
    </style>
    <style name="TimeMateEditText">
        <item name="android:background">@drawable/timemate_edittext_background</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:padding">12dp</item>
    </style>
    <style name="TimeMateTextBody">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TimeMateTextCaption">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TimeMateTextSubtitle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TimeMateTextTemperature">
        <item name="android:textSize">48sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif-light</item>
    </style>
    <style name="TimeMateTextTitle">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TimeMateTheme" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="colorPrimary">@color/sky_blue_primary</item>
        <item name="colorPrimaryVariant">@color/sky_blue_dark</item>
        <item name="colorOnPrimary">@color/text_primary</item>
        <item name="colorSecondary">@color/sky_blue_accent</item>
        <item name="colorSecondaryVariant">@color/sky_blue_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="android:colorBackground">@color/background_secondary</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorSurface">@color/card_background</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="android:statusBarColor">@color/background_secondary</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
    </style>
    <style name="iOSBody">
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>
    <style name="iOSCallout">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="iOSCaption">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">0</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="iOSEditText">
        <item name="android:layout_height">50dp</item>
        <item name="android:background">@drawable/timemate_edittext_background</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/text_input</item>
        <item name="android:textColorHint">@color/text_input_hint</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="iOSFootnote">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/text_tertiary</item>
        <item name="android:fontFamily">@font/pretendard_regular</item>
        <item name="android:letterSpacing">-0.01</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="iOSHeadline">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_bold</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="iOSSubheadline">
        <item name="android:textSize">17sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_medium</item>
        <item name="android:letterSpacing">-0.02</item>
        <item name="android:includeFontPadding">false</item>
    </style>
    <style name="iOSTitle">
        <item name="android:textSize">28sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/pretendard_bold</item>
        <item name="android:letterSpacing">-0.03</item>
        <item name="android:includeFontPadding">false</item>
    </style>
</resources>