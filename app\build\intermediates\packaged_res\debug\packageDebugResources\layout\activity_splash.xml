<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FDF5F7">

    <!-- 중앙 컨텐츠 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:gravity="center">

        <!-- 앱 로고 -->
        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@mipmap/app_icon"
            android:layout_marginBottom="20dp" />

        <!-- 앱 이름 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="TimeMate"
            android:textSize="28sp"
            android:textStyle="bold"
            android:textColor="#F07171"
            android:layout_marginBottom="8dp" />

        <!-- 앱 설명 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="당신의 시간을 더 스마트하게"
            android:textSize="14sp"
            android:textColor="#666666"
            android:layout_marginBottom="40dp" />

    </LinearLayout>

    <!-- 하단 로딩 인디케이터 -->
    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="80dp"
        android:indeterminateTint="#F07171" />

</RelativeLayout>
