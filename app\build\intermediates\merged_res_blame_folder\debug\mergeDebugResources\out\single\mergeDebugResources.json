[{"merged": "com.example.timemate.app-debug-41:/drawable_ic_route_bus.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_route_bus.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_tomorrow_reminder_card.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_tomorrow_reminder_card.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_tab_container.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_tab_container.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_manual_login.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_manual_login.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_category_button_selector.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_category_button_selector.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_edit.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_edit.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_ootd_recommendation.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_ootd_recommendation.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_friend_selection.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_friend_selection.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_schedule_list.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_schedule_list.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_icon_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_icon_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_arrow_forward.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_arrow_forward.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_recommendation.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_recommendation.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_friend_list.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_friend_list.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_account_switch.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_account_switch.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_place_suggestion.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_place_suggestion.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_time.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_time.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_button_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_button_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_home.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_home.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_notifications.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_notifications.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_app_icon.png.flat", "source": "com.example.timemate.app-main-43:/drawable/app_icon.png"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_directions.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_directions.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_dialog_schedule_detail_improved.xml.flat", "source": "com.example.timemate.app-main-43:/layout/dialog_schedule_detail_improved.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.timemate.app-debug-41:/menu_bottom_nav_menu.xml.flat", "source": "com.example.timemate.app-main-43:/menu/bottom_nav_menu.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_status_badge_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/status_badge_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_today_schedule.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_today_schedule.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_schedule_notification.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_schedule_notification.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_account.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_account.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_circle_background_ios.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/circle_background_ios.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_bg_tag_rounded.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/bg_tag_rounded.xml"}, {"merged": "com.example.timemate.app-debug-41:/anim_fade_out.xml.flat", "source": "com.example.timemate.app-main-43:/anim/fade_out.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_arrow_back.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_arrow_back.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_directions_car.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_directions_car.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_launcher_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_route_recommended_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/route_recommended_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_location.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_location.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_directions_walk.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_directions_walk.xml"}, {"merged": "com.example.timemate.app-debug-41:/anim_fade_in.xml.flat", "source": "com.example.timemate.app-main-43:/anim/fade_in.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_indicator_dot_inactive.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/indicator_dot_inactive.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_chip_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_chip_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_directions_transit.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_directions_transit.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_button_accept.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/button_accept.xml"}, {"merged": "com.example.timemate.app-debug-41:/xml_backup_rules.xml.flat", "source": "com.example.timemate.app-main-43:/xml/backup_rules.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_badge_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_badge_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_schedule_reminder_detail.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_schedule_reminder_detail.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_schedule.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_schedule.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_friends.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_friends.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_button_secondary_ios.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/button_secondary_ios.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_circle_dot.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/circle_dot.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_button_primary.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/button_primary.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_card_completed.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/card_completed.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_timemate_button_outlined.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/timemate_button_outlined.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_schedule_list.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_schedule_list.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_home_schedule.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_home_schedule.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_dialog_directions_bottom_sheet.xml.flat", "source": "com.example.timemate.app-main-43:/layout/dialog_directions_bottom_sheet.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_notifications.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_notifications.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_schedule_add.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_schedule_add.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_badge_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/badge_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_transit.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_transit.xml"}, {"merged": "com.example.timemate.app-debug-41:/font_pretendard_medium.xml.flat", "source": "com.example.timemate.app-main-43:/font/pretendard_medium.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_check_circle.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_check_circle.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_count_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_count_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_category_tag_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/category_tag_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_map_error.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_map_error.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_dialog_schedule_detail_ios.xml.flat", "source": "com.example.timemate.app-main-43:/layout/dialog_schedule_detail_ios.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_signup_form.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_signup_form.xml"}, {"merged": "com.example.timemate.app-debug-41:/color_bottom_nav_color.xml.flat", "source": "com.example.timemate.app-main-43:/color/bottom_nav_color.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_friend.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_friend.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_image_error.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_image_error.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_circle_button_white.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_circle_button_white.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_recommended_badge.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/recommended_badge.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_card_overdue.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/card_overdue.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_check.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_check.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_edit_text_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/edit_text_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_route_card.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_route_card.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_walking.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_walking.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_header_blue_gradient.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_header_blue_gradient.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_password_reset.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_password_reset.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_tab_unselected.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_tab_unselected.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_button_outline.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/button_outline.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_profile.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_profile.xml"}, {"merged": "com.example.timemate.app-debug-41:/xml_data_extraction_rules.xml.flat", "source": "com.example.timemate.app-main-43:/xml/data_extraction_rules.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_friend_add.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_friend_add.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_dialog_schedule_detail.xml.flat", "source": "com.example.timemate.app-main-43:/layout/dialog_schedule_detail.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_location_end.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_location_end.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_arrow_right.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_arrow_right.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_tab_selected.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_tab_selected.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_notification.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_notification.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_location_start.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_location_start.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_add.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_add.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_notification.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_notification.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_driving.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_driving.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.timemate.app-debug-41:/drawable_timemate_edittext_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/timemate_edittext_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_snooze.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_snooze.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_access_time.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_access_time.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_person.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_person.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.timemate.app-debug-41:/layout_dialog_friend_selection.xml.flat", "source": "com.example.timemate.app-main-43:/layout/dialog_friend_selection.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_location_on.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_location_on.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_card_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_card_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_search_button.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_search_button.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_route_car.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_route_car.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_route_normal_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/route_normal_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_tomorrow_reminder_header.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_tomorrow_reminder_header.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_button_primary_ios.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/button_primary_ios.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_schedule_detail.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_schedule_detail.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_schedule_header.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_schedule_header.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_close.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_close.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.timemate.app-debug-41:/drawable_circle_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/circle_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_timemate_button_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/timemate_button_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_navigation_button.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_navigation_button.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_bottom_sheet_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/bottom_sheet_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_schedule_calendar.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_schedule_calendar.xml"}, {"merged": "com.example.timemate.app-debug-41:/font_pretendard_regular.xml.flat", "source": "com.example.timemate.app-main-43:/font/pretendard_regular.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_home.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_home.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_recommendation.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_recommendation.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.timemate.app-main-43:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_calendar.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_calendar.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_image_placeholder.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_image_placeholder.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_profile.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_profile.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_schedule.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_schedule.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_route_option.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_route_option.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_modal_handle.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/modal_handle.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_place_autocomplete.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_place_autocomplete.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_distance_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_distance_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_place_with_image.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_place_with_image.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.timemate.app-main-43:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.timemate.app-debug-41:/animator_card_elevation_animator.xml.flat", "source": "com.example.timemate.app-main-43:/animator/card_elevation_animator.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_cancel.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_cancel.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_card_normal.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/card_normal.xml"}, {"merged": "com.example.timemate.app-debug-41:/color_ios_category_text_selector.xml.flat", "source": "com.example.timemate.app-main-43:/color/ios_category_text_selector.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_route_walk.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_route_walk.xml"}, {"merged": "com.example.timemate.app-debug-41:/font_pretendard_bold.xml.flat", "source": "com.example.timemate.app-main-43:/font/pretendard_bold.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_schedule_info_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/schedule_info_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_category_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/category_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_phone.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_phone.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_weather_card_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/weather_card_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_timemate_button_primary.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/timemate_button_primary.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_card_selected.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/card_selected.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_indicator_dot_active.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/indicator_dot_active.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_map_placeholder.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_map_placeholder.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_weather.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_weather.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_dialog_route_options.xml.flat", "source": "com.example.timemate.app-main-43:/layout/dialog_route_options.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_place_suggest.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_place_suggest.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_button_reject.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/button_reject.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_item_schedule_improved.xml.flat", "source": "com.example.timemate.app-main-43:/layout/item_schedule_improved.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_notification_badge_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/notification_badge_background.xml"}, {"merged": "com.example.timemate.app-debug-41:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.timemate.app-main-43:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ic_image_placeholder.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ic_image_placeholder.xml"}, {"merged": "com.example.timemate.app-debug-41:/layout_activity_main.xml.flat", "source": "com.example.timemate.app-main-43:/layout/activity_main.xml"}, {"merged": "com.example.timemate.app-debug-41:/drawable_ios_rating_background.xml.flat", "source": "com.example.timemate.app-main-43:/drawable/ios_rating_background.xml"}]