<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 헤더 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/sky_blue"
            android:orientation="horizontal"
            android:padding="20dp"
            android:gravity="center_vertical">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                app:tint="@android:color/white"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="일정 초대 알림"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@android:color/white" />

            <TextView
                android:id="@+id/textNotificationCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0개"
                android:textSize="16sp"
                android:textColor="#E1BEE7" />

        </LinearLayout>

        <!-- 탭 레이아웃 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_margin="16dp"
            android:background="@drawable/ios_card_background"
            android:padding="4dp">

            <Button
                android:id="@+id/btnTabPending"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="대기 중"
                android:textSize="14sp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/btnTabAll"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:text="전체"
                android:textSize="14sp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- 알림 리스트 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="16dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerNotifications"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="16dp" />

            <!-- 빈 상태 -->
            <LinearLayout
                android:id="@+id/layoutEmptyState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📬"
                    android:textSize="64sp"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="받은 초대가 없습니다"
                    style="@style/TimeMateTextBody"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="친구들이 일정을 공유하면 여기에 표시됩니다"
                    style="@style/TimeMateTextCaption"
                    android:textColor="@color/text_secondary"
                    android:gravity="center" />

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
