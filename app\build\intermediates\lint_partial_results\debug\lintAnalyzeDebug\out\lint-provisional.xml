<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.0" type="conditional_incidents">

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/NotificationService.java"
            line="69"
            column="13"
            startOffset="3008"
            endLine="69"
            endColumn="72"
            endOffset="3067"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/NotificationService.java"
            line="94"
            column="13"
            startOffset="4200"
            endLine="94"
            endColumn="72"
            endOffset="4259"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/NotificationService.java"
            line="121"
            column="13"
            startOffset="5479"
            endLine="121"
            endColumn="72"
            endOffset="5538"/>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/notification/ReminderNotificationHelper.java"
            line="104"
            column="13"
            startOffset="4220"
            endLine="104"
            endColumn="69"
            endOffset="4276"/>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/AccountSwitchActivity.java"
            line="332"
            column="24"
            startOffset="12341"
            endLine="332"
            endColumn="39"
            endOffset="12356"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="480"
            column="24"
            startOffset="17266"
            endLine="480"
            endColumn="39"
            endOffset="17281"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/ImprovedScheduleAdapter.java"
            line="208"
            column="33"
            startOffset="8143"
            endLine="208"
            endColumn="48"
            endOffset="8158"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="247"
            column="29"
            startOffset="9766"
            endLine="247"
            endColumn="44"
            endOffset="9781"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_access_time.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_access_time.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_forward.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_forward.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_right.xml"
            line="9"
            column="28"
            startOffset="263"
            endLine="9"
            endColumn="48"
            endOffset="283"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cancel.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cancel.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_car.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_car.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_transit.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_transit.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_walk.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_walk.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_driving.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="41"
            endOffset="260"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_driving.xml"
            line="10"
            column="28"
            startOffset="305"
            endLine="10"
            endColumn="48"
            endOffset="325"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="36"
            endOffset="216"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit.xml"
            line="8"
            column="26"
            startOffset="252"
            endLine="8"
            endColumn="46"
            endOffset="272"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_friends.xml"
            line="3"
            column="30"
            startOffset="221"
            endLine="3"
            endColumn="50"
            endOffset="241"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_error.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="35"
            endOffset="254"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_error.xml"
            line="10"
            column="28"
            startOffset="299"
            endLine="10"
            endColumn="48"
            endOffset="319"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_placeholder.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="35"
            endOffset="254"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_placeholder.xml"
            line="10"
            column="28"
            startOffset="299"
            endLine="10"
            endColumn="48"
            endOffset="319"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location.xml"
            line="8"
            column="24"
            startOffset="223"
            endLine="8"
            endColumn="54"
            endOffset="253"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_end.xml"
            line="8"
            column="26"
            startOffset="242"
            endLine="8"
            endColumn="46"
            endOffset="262"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_on.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_on.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_start.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="36"
            endOffset="216"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_start.xml"
            line="8"
            column="26"
            startOffset="252"
            endLine="8"
            endColumn="46"
            endOffset="272"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_map_error.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="40"
            endOffset="259"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_map_error.xml"
            line="10"
            column="28"
            startOffset="304"
            endLine="10"
            endColumn="48"
            endOffset="324"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_map_placeholder.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="41"
            endOffset="260"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_map_placeholder.xml"
            line="10"
            column="28"
            startOffset="305"
            endLine="10"
            endColumn="48"
            endOffset="325"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_person.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="36"
            endOffset="216"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_person.xml"
            line="8"
            column="26"
            startOffset="252"
            endLine="8"
            endColumn="46"
            endOffset="272"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_phone.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="34"
            endOffset="253"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_phone.xml"
            line="10"
            column="28"
            startOffset="298"
            endLine="10"
            endColumn="48"
            endOffset="318"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="10"
            column="26"
            startOffset="274"
            endLine="10"
            endColumn="52"
            endOffset="300"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="11"
            column="28"
            startOffset="329"
            endLine="11"
            endColumn="48"
            endOffset="349"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="19"
            column="26"
            startOffset="631"
            endLine="19"
            endColumn="52"
            endOffset="657"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="20"
            column="28"
            startOffset="686"
            endLine="20"
            endColumn="48"
            endOffset="706"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_bus.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_bus.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_car.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="38"
            endOffset="257"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_car.xml"
            line="10"
            column="28"
            startOffset="302"
            endLine="10"
            endColumn="48"
            endOffset="322"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_walk.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_walk.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_schedule.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_schedule.xml"
            line="5"
            column="30"
            startOffset="460"
            endLine="5"
            endColumn="50"
            endOffset="480"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_schedule_notification.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="31"
            endOffset="250"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_schedule_notification.xml"
            line="10"
            column="28"
            startOffset="295"
            endLine="10"
            endColumn="48"
            endOffset="315"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_snooze.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="39"
            endOffset="258"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_snooze.xml"
            line="10"
            column="28"
            startOffset="303"
            endLine="10"
            endColumn="48"
            endOffset="323"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_time.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_time.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_time.xml"
            line="11"
            column="26"
            startOffset="489"
            endLine="11"
            endColumn="46"
            endOffset="509"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_transit.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="41"
            endOffset="260"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_transit.xml"
            line="10"
            column="28"
            startOffset="305"
            endLine="10"
            endColumn="48"
            endOffset="325"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_walking.xml"
            line="7"
            column="19"
            startOffset="238"
            endLine="7"
            endColumn="41"
            endOffset="260"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_walking.xml"
            line="10"
            column="28"
            startOffset="305"
            endLine="10"
            endColumn="48"
            endOffset="325"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_weather.xml"
            line="6"
            column="28"
            startOffset="209"
            endLine="6"
            endColumn="48"
            endOffset="229"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1245"
                endOffset="1265"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="25"
            column="9"
            startOffset="1245"
            endLine="25"
            endColumn="29"
            endOffset="1265"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
