C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ManualLoginActivity.java:224: Error: Overriding method should call super.onBackPressed [MissingSuperCall]
    public void onBackPressed() {
                ~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\PasswordResetActivity.java:215: Error: Overriding method should call super.onBackPressed [MissingSuperCall]
    public void onBackPressed() {
                ~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\SignupFormActivity.java:170: Error: Overriding method should call super.onBackPressed [MissingSuperCall]
    public void onBackPressed() {
                ~~~~~~~~~~~~~

   Explanation for issues of type "MissingSuperCall":
   Some methods, such as View#onDetachedFromWindow, require that you also call
   the super implementation as part of your method.

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\notification\NotificationActivity.java:78: Error: Unexpected implicit cast to TextView: layout tag was LinearLayout [WrongViewCast]
            textEmptyNotifications = findViewById(R.id.textEmptyNotifications);
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "WrongViewCast":
   Keeps track of the view types associated with ids and if it finds a usage
   of the id in the Java code it ensures that it is treated as the same type.

C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:55: Error: Class referenced in the manifest, com.example.timemate.ui.home.HomeActivity, was not found in the project or the libraries [MissingClass]
        <activity android:name=".ui.home.HomeActivity" />
                                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:56: Error: Class referenced in the manifest, com.example.timemate.ui.schedule.ScheduleListActivity, was not found in the project or the libraries [MissingClass]
        <activity android:name=".ui.schedule.ScheduleListActivity" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:57: Error: Class referenced in the manifest, com.example.timemate.ui.friend.FriendListActivity, was not found in the project or the libraries [MissingClass]
        <activity android:name=".ui.friend.FriendListActivity" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\AndroidManifest.xml:58: Error: Class referenced in the manifest, com.example.timemate.ui.profile.ProfileActivity, was not found in the project or the libraries [MissingClass]
        <activity android:name=".ui.profile.ProfileActivity" />
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingClass":
   If a class is referenced in the manifest or in a layout file, it must also
   exist in the project (or in one of the libraries included by the project.
   This check helps uncover typos in registration names, or attempts to rename
   or move classes without updating the XML references properly.

   https://developer.android.com/guide/topics/manifest/manifest-intro.html

C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_bold.xml:6: Error: Font pretendard_bold should not reference itself [ResourceCycle]
        android:font="@font/pretendard_bold" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_bold.xml:6: Error: Font pretendard_bold should not reference itself [ResourceCycle]
        android:font="@font/pretendard_bold" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_medium.xml:6: Error: Font pretendard_medium should not reference itself [ResourceCycle]
        android:font="@font/pretendard_medium" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_medium.xml:6: Error: Font pretendard_medium should not reference itself [ResourceCycle]
        android:font="@font/pretendard_medium" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_regular.xml:6: Error: Font pretendard_regular should not reference itself [ResourceCycle]
        android:font="@font/pretendard_regular" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\font\pretendard_regular.xml:6: Error: Font pretendard_regular should not reference itself [ResourceCycle]
        android:font="@font/pretendard_regular" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ResourceCycle":
   There should be no cycles in resource definitions as this can lead to
   runtime exceptions.

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\core\util\DistanceCalculator.java:244: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%,d원", totalCost);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\core\util\DistanceCalculator.java:262: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%,d원 (연료비 + 톨게이트)", totalCost);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\core\util\DistanceCalculator.java:264: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%,d원", totalCost);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\core\util\DistanceCalculator.java:274: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1fkm", km);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\core\util\DistanceCalculator.java:276: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.0fm", distanceInMeters);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\service\DummyPlaceSearchService.java:302: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1fkm", meters / 1000.0);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\EnhancedDirectionsService.java:140: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String distance = String.format("%.1f km", distanceM / 1000.0);
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\HomeActivity.java:306: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            textTemperature.setText(String.format("%.0f°", weather.getTemperature()));
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\HomeActivity.java:315: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            textFeelsLike.setText(String.format("체감 %.0f°", weather.getFeelsLike()));
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\HomeActivity.java:318: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            textHumidity.setText(String.format("습도 %d%%", weather.getHumidity()));
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\recommendation\ImageCrawlingService.java:146: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerUrl = url.toLowerCase();
                              ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\recommendation\ImageCrawlingService.java:158: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerUrl = url.toLowerCase();
                              ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\LocalPlaceSearchService.java:130: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerKeyword = keyword.toLowerCase();
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\LocalPlaceSearchService.java:133: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            if (place.name.toLowerCase().contains(lowerKeyword) ||
                           ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\LocalPlaceSearchService.java:134: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                place.address.toLowerCase().contains(lowerKeyword) ||
                              ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\LocalPlaceSearchService.java:135: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                place.category.toLowerCase().contains(lowerKeyword)) {
                               ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:260: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    String.format("%.1f km", distance),
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:355: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String walkingDistance = String.format("%.1f km", distance);
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:396: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String bicycleDistance = String.format("%.1f km", distance);
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:453: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String busDistance = String.format("%.1f km", distance);
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:495: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String subwayDistance = String.format("%.1f km", distance);
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:545: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String distance = String.format("%.1f km", distanceInMeters / 1000.0);
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:553: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String cost = String.format("%,d원", totalCost);
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:590: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String drivingDistance = String.format("%.1f km", distance);
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:592: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String drivingCost = String.format("%,d원", estimatedCost);
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:631: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String trainDistance = String.format("%.1f km", distance);
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:676: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        String flightDistance = String.format("%.1f km", distance);
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\MultiModalRouteService.java:1042: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String distance = String.format("%.1f km", distanceInMeters / 1000.0);
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverDirectionsService.java:238: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String distance = String.format("%.1f km", distanceInMeters / 1000.0);
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverDirectionsService.java:242: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String duration = String.format("%d분", durationInMs / (1000 * 60));
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\NaverLocalSearchService.java:500: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (location.toLowerCase()) {
                         ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\NaverLocalSearchService.java:530: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (location.toLowerCase()) {
                         ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverOptimalRouteService.java:81: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                return String.format("%.1fkm", distance / 1000.0);
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverOptimalRouteService.java:89: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                return String.format("%,d원", cost);
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverPlaceKeywordService.java:370: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerKeyword = keyword.toLowerCase();
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverPlaceSearchService.java:84: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%s (%s) - %.1f★", name, category, rating);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverPlaceSearchService.java:519: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerName = placeName.toLowerCase();
                                     ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverPlaceSearchService.java:520: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerAddress = address.toLowerCase();
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverPlaceSearchService.java:573: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerName = placeName.toLowerCase();
                                     ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverPlaceSearchService.java:574: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerAddress = address.toLowerCase();
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverTransitService.java:269: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1fkm", distanceInKm);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverTransitService.java:271: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.0fm", distanceInKm * 1000);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\NaverTransitService.java:279: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%,d원", price);
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\OOTDAdapter.java:128: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                switch (brand.toLowerCase()) {
                              ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\model\OOTDRecommendation.java:41: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                              weather.toLowerCase().contains(currentWeather.toLowerCase());
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\model\OOTDRecommendation.java:41: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                              weather.toLowerCase().contains(currentWeather.toLowerCase());
                                                                            ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\model\OOTDRecommendation.java:43: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                             season.toLowerCase().contains(currentSeason.toLowerCase());
                                    ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\model\OOTDRecommendation.java:43: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                             season.toLowerCase().contains(currentSeason.toLowerCase());
                                                                         ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\adapters\OOTDRecommendationAdapter.java:124: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            switch (category.toLowerCase()) {
                             ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\adapters\OOTDRecommendationAdapter.java:145: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            switch (category.toLowerCase()) {
                             ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\ootd\OOTDRecommendationService.java:182: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerWeather = weather.toLowerCase();
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\service\OOTDRecommendationService.java:518: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String itemName = item.name.toLowerCase();
                                    ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\profile\ProfileActivity.java:216: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        textScheduleCount.setText(String.format("📅 총 %d개 (완료: %d개)", scheduleCount, completedScheduleCount));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\profile\ProfileActivity.java:219: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        textFriendCount.setText(String.format("👥 %d명", friendCount));
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\RealTimeTrafficService.java:159: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    String weatherMain = weather.main.toLowerCase();
                                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\recommendation\RecommendationActivity.java:1089: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1fkm", km);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\recommendation\RecommendationAdapter.java:107: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            textPlaceRating.setText(String.format("⭐ %.1f", place.rating));
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\notification\ReminderNotificationHelper.java:69: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String content = String.format("%s → %s\n예상 %d분 소요 (%s)",
                             ^
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\RetrofitNaverDirectionsService.java:263: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1fkm", km);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\RetrofitNaverDirectionsService.java:290: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%,d원", price);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:915: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("%.1fkm", distance),
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:927: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("%.1fkm", distance),
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:939: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("%.1fkm", distance),
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:951: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("%.1fkm", distance),
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:963: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String.format("%.1fkm", distance),
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1037: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                return String.format("약 %d시간 %d분", hours, remainingMinutes);
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1039: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                return String.format("약 %d분", minutes);
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1054: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    return String.format("%,d원", drivingCost);
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1057: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    return String.format("%,d원", taxiCost);
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1060: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    return String.format("%,d원", transitCost);
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1270: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String todayString = String.format("%04d-%02d-%02d",
                                 ^
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1301: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    return String.format("%04d-%02d-%02d", year, month, day);
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1483: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                    " (" + String.format("%.1f", displayDistance) + "km)" +
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1527: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String userMessage = String.format("📍 거리: %.1fkm\n원하는 교통수단을 선택해주세요", displayDistance);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1662: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String successMessage = String.format("✅ %d개 교통수단이 선택되어 일정에 저장됩니다", selectedModes.size());
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1737: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            routeInfoJson.append("\"distance\":\"").append(String.format("%.2f", distance)).append("km\",");
                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1761: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        cost = String.format("%,d원", publicCost);
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1795: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                routeInfoJson.append("\"distance\":\"").append(String.format("%.2f", distance)).append("km\",");
                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1858: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                                .append(", ").append(String.format("%,d", publicCost)).append("원\n");
                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1889: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            String message = String.format("경로가 선택되었습니다 (%d개 교통수단)", selectedModes.size());
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2126: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerName = placeName.toLowerCase();
                                     ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2127: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerAddress = address.toLowerCase();
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2261: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "최단거리 경로 (" + String.format("%.1f", distance) + "km)"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2268: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "자전거 경로 (" + String.format("%.1f", distance) + "km)"
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2275: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "도보 경로 (" + String.format("%.1f", distance) + "km)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2282: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                "택시 경로 (" + String.format("%.1f", distance) + "km)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2409: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                timeInfo += " (" + String.format("%,d", option.costWon) + "원)";
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2776: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            switch (original.transportMode.toLowerCase()) {
                                           ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2848: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                    route.transportMode.name().toLowerCase(),
                                               ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleCalendarActivity.java:417: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (mode.toLowerCase()) {
                     ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleListActivity.java:893: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (mode.toLowerCase()) {
                     ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleListActivity.java:907: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (mode.toLowerCase()) {
                     ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminder.java:95: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                return String.format("%02d:%02d", departureHour, departureMinute);
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminder.java:132: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format("%s → %s (%s, 약 %d분 소요)", 
               ^
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\worker\ScheduleWorker.java:304: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        String lowerAddress = address.toLowerCase();
                                      ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\WeatherData.java:94: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        switch (description.toLowerCase()) {
                            ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\service\WeatherService.java:42: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%s %.1f°C %s", city, temperature, description);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\WeatherService.java:44: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%s, %.1f°C (체감 %.1f°C), 습도 %d%%", 
                   ^
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\service\WeatherService.java:46: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%s\n%.1f°C %s\n습도: %d%% | 바람: %.1fm/s",
                   ^
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\network\api\WeatherService.java:63: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                String urlString = String.format(
                                   ^

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\HomePresenter.java:63: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
                java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\HomePresenter.java:107: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
                java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\HomePresenter.java:148: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
                java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\model\Schedule.java:84: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm");
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\model\Schedule.java:93: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\model\Schedule.java:94: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
            java.text.SimpleDateFormat timeFormat = new java.text.SimpleDateFormat("HH:mm");
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\data\repository\ScheduleRepository.java:154: Warning: To get local formatting use getDateInstance(), getDateTimeInstance(), or getTimeInstance(), or use new SimpleDateFormat(String template, Locale locale) with for example Locale.US for ASCII dates. [SimpleDateFormat]
                java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SimpleDateFormat":
   Almost all callers should use getDateInstance(), getDateTimeInstance(), or
   getTimeInstance() to get a ready-made instance of SimpleDateFormat suitable
   for the user's locale. The main reason you'd create an instance this class
   directly is because you need to format/parse a specific machine-readable
   format, in which case you almost certainly want to explicitly ask for US to
   ensure that you get ASCII digits (rather than, say, Arabic digits).

   Therefore, you should either use the form of the SimpleDateFormat
   constructor where you pass in an explicit locale, such as Locale.US, or use
   one of the get instance methods, or suppress this error if really know what
   you are doing.

   https://developer.android.com/reference/java/text/SimpleDateFormat.html

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1413: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
                dialogView = getLayoutInflater().inflate(R.layout.dialog_route_options, null);
                                                                                        ~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleListActivity.java:971: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
                dialogView = inflater.inflate(R.layout.dialog_schedule_detail_ios, null);
                                                                                   ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\profile\ProfileActivity.java:60: Warning: Use SwitchCompat from AppCompat or MaterialSwitch from Material library [UseSwitchCompatOrMaterialCode from androidx.appcompat]
    private Switch switchRealtimeData;
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseSwitchCompatOrMaterialCode":
   Use SwitchCompat from AppCompat or MaterialSwitch from Material library

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:303: Warning: Use SwitchCompat from AppCompat or MaterialSwitch from Material library [UseSwitchCompatOrMaterialXml from androidx.appcompat]
            <Switch
            ^

   Explanation for issues of type "UseSwitchCompatOrMaterialXml":
   Use SwitchCompat from AppCompat or MaterialSwitch from Material library

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\TimeMate\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.0 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.0"
      ~~~~~~~
C:\Users\<USER>\TimeMate\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.0 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.0"
      ~~~~~~~
C:\Users\<USER>\TimeMate\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.0 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.0"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\Users\<USER>\TimeMate\app\build.gradle.kts:67: Warning: A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1 [GradleDependency]
    annotationProcessor("androidx.room:room-compiler:2.6.1")
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:68: Warning: A newer version of com.kakao.sdk:v2-user than 2.19.0 is available: 2.21.3 [GradleDependency]
    implementation ("com.kakao.sdk:v2-user:2.19.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:84: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:87: Warning: A newer version of androidx.work:work-runtime than 2.8.1 is available: 2.10.1 [GradleDependency]
    implementation("androidx.work:work-runtime:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:100: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                            android:textSize="24dp"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:200: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                            android:textSize="24dp"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:300: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                            android:textSize="24dp"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:400: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                            android:textSize="24dp"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:500: Warning: Should use "sp" instead of "dp" for text sizes [SpUsage]
                            android:textSize="24dp"
                            ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SpUsage":
   When setting text sizes, you should normally use sp, or "scale-independent
   pixels". This is like the dp unit, but it is also scaled by the user's font
   size preference. It is recommend you use this unit when specifying font
   sizes, so they will be adjusted for both the screen density and the user's
   preference.

   There are cases where you might need to use dp; typically this happens when
   the text is in a container with a specific dp-size. This will prevent the
   text from spilling outside the container. Note however that this means that
   the user's font size settings are not respected, so consider adjusting the
   layout itself to be more flexible.

   https://developer.android.com/training/multiscreen/screendensities.html

C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_home_schedule.xml:66: Error: Must use app:tint instead of android:tint [UseAppTint from androidx.appcompat]
        android:tint="@color/text_hint" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseAppTint":
   ImageView or ImageButton uses android:tint instead of app:tint

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:132: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
                android:drawableStart="@drawable/ic_phone"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:160: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
                android:drawableStart="@drawable/ic_person"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:54: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
                android:drawableStart="@drawable/ic_access_time"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:67: Warning: Use app:drawableStartCompat instead of android:drawableStart [UseCompatTextViewDrawableXml from androidx.appcompat]
                android:drawableStart="@drawable/ic_location_on"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseCompatTextViewDrawableXml":
   TextView uses android: compound drawable attributes instead of app: ones

   Vendor: Android Open Source Project
   Identifier: androidx.appcompat
   Feedback: https://issuetracker.google.com/issues/new?component=460343

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\AccountAdapter.java:120: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\AccountSwitchActivity.java:209: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                        adapter.notifyDataSetChanged();
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\friend\FriendListActivity.java:154: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                    friendAdapter.notifyDataSetChanged();
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\friend\adapter\FriendListAdapter.java:60: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\adapter\ImprovedScheduleAdapter.java:233: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\adapters\NotificationAdapter.java:58: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\OOTDAdapter.java:62: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\adapters\OOTDRecommendationAdapter.java:55: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\adapter\PlaceSuggestAdapter.java:90: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\adapter\PlaceSuggestAdapter.java:95: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\recommendation\PlaceWithImageAdapter.java:51: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\recommendation\RecommendationActivity.java:1887: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                        imageAdapter.notifyDataSetChanged();
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\recommendation\RecommendationActivity.java:1895: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                            imageAdapter.notifyDataSetChanged();
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\recommendation\RecommendationAdapter.java:67: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\adapter\RouteOptionAdapter.java:46: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleCalendarActivity.java:232: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        scheduleAdapter.notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleCalendarActivity.java:503: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                        scheduleAdapter.notifyDataSetChanged();
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\ScheduleDetailAdapter.java:139: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleListActivity.java:704: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                        scheduleAdapter.notifyDataSetChanged();
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleListActivity.java:1213: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                        scheduleAdapter.notifyDataSetChanged();
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\adapter\ScheduleListAdapter.java:63: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\TodayScheduleAdapter.java:62: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\TomorrowReminderAdapter.java:83: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\NotificationService.java:31: Warning: Unnecessary; SDK_INT is always >= 27 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\notification\ReminderNotificationHelper.java:40: Warning: Unnecessary; SDK_INT is always >= 27 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:662: Warning: Unnecessary; SDK_INT is always >= 27 [ObsoleteSdkInt]
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:314: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                    <LinearLayout
                     ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:395: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
                <LinearLayout
                 ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:59: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
            <LinearLayout
             ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:19: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:65: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
            <LinearLayout
             ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:21: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:54: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
        <LinearLayout
         ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:78: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
        <LinearLayout
         ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:104: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:19: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
        <LinearLayout
         ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:57: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
        <LinearLayout
         ~~~~~~~~~~~~

   Explanation for issues of type "UseCompoundDrawables":
   A LinearLayout which contains an ImageView and a TextView can be more
   efficiently handled as a compound drawable (a single TextView, using the
   drawableTop, drawableLeft, drawableRight and/or drawableBottom attributes
   to draw one or more images adjacent to the text).

   If the two widgets are offset from each other with margins, this can be
   replaced with a drawablePadding attribute.

   There's a lint quickfix to perform this conversion in the Eclipse plugin.

C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_weather.xml:7: Warning: Very long vector path (944 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
        android:pathData="M12,2C13.1,2 14,2.9 14,4C14,5.1 13.1,6 12,6C10.9,6 10,5.1 10,4C10,2.9 10.9,2 12,2M21,11H23C23.6,11 24,11.4 24,12C24,12.6 23.6,13 23,13H21C20.4,13 20,12.6 20,12C20,11.4 20.4,11 21,11M1,11H3C3.6,11 4,11.4 4,12C4,12.6 3.6,13 3,13H1C0.4,13 0,12.6 0,12C0,11.4 0.4,11 1,11M13,1V3C13,3.6 12.6,4 12,4C11.4,4 11,3.6 11,3V1C11,0.4 11.4,0 12,0C12.6,0 13,0.4 13,1M13,21V23C13,23.6 12.6,24 12,24C11.4,24 11,23.6 11,23V21C11,20.4 11.4,20 12,20C12.6,20 13,20.4 13,21M4.2,3.4C4.6,3 5.2,3 5.6,3.4L7,4.8C7.4,5.2 7.4,5.8 7,6.2C6.6,6.6 6,6.6 5.6,6.2L4.2,4.8C3.8,4.4 3.8,3.8 4.2,3.4M18.4,17.6C18.8,17.2 19.4,17.2 19.8,17.6C20.2,18 20.2,18.6 19.8,19L18.4,20.4C18,20.8 17.4,20.8 17,20.4C16.6,20 16.6,19.4 17,19L18.4,17.6M19.8,3.4C20.2,3.8 20.2,4.4 19.8,4.8L18.4,6.2C18,6.6 17.4,6.6 17,6.2C16.6,5.8 16.6,5.2 17,4.8L18.4,3.4C18.8,3 19.4,3 19.8,3.4M5.6,17.6L7,19C7.4,19.4 7.4,20 7,20.4C6.6,20.8 6,20.8 5.6,20.4L4.2,19C3.8,18.6 3.8,18 4.2,17.6C4.6,17.2 5.2,17.2 5.6,17.6Z"/>
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:57: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
                <LinearLayout
                 ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:207: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
            <LinearLayout
             ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:16: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule.xml:16: Warning: Set android:baselineAligned="false" on this element for better performance [DisableBaselineAlignment]
    <LinearLayout
     ~~~~~~~~~~~~

   Explanation for issues of type "DisableBaselineAlignment":
   When a LinearLayout is used to distribute the space proportionally between
   nested layouts, the baseline alignment property should be turned off to
   make the layout computation faster.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:101: Warning: Nested weights are bad for performance [NestedWeights]
            android:layout_weight="1"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_ootd_recommendation.xml:74: Warning: Nested weights are bad for performance [NestedWeights]
                android:layout_weight="1"
                ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NestedWeights":
   Layout weights require a widget to be measured twice. When a LinearLayout
   with non-zero weights is nested inside another LinearLayout with non-zero
   weights, then the number of measurements increase exponentially.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:7: Warning: Possible overdraw: Root element paints background @color/background_secondary with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background_secondary">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:7: Warning: Possible overdraw: Root element paints background @color/background_primary with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background_primary">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml:6: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="#f5f5f5">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:7: Warning: Possible overdraw: Root element paints background @color/background_secondary with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background_secondary">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notification.xml:7: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="#f5f5f5">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:6: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background_light">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:5: Warning: Possible overdraw: Root element paints background @color/background with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:7: Warning: Possible overdraw: Root element paints background #f5f5f5 with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="#f5f5f5">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:6: Warning: Possible overdraw: Root element paints background @color/background with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:7: Warning: Possible overdraw: Root element paints background @color/background_secondary with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background_secondary">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_calendar.xml:7: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background_light"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:6: Warning: Possible overdraw: Root element paints background @color/background with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:7: Warning: Possible overdraw: Root element paints background @color/background_light with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="@color/background_light">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_autocomplete.xml:7: Warning: Possible overdraw: Root element paints background ?android:attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="?android:attr/selectableItemBackground">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggest.xml:8: Warning: Possible overdraw: Root element paints background ?android:attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="?android:attr/selectableItemBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggestion.xml:8: Warning: Possible overdraw: Root element paints background ?android:attr/selectableItemBackground with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="?android:attr/selectableItemBackground"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:9: Warning: Possible overdraw: Root element paints background #33B5DFFF with a theme that also paints a background (inferred theme is @style/TimeMateTheme) [Overdraw]
    android:background="#33B5DFFF">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_main.xml:1: Warning: The resource R.layout.activity_main appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:2: Warning: The resource R.layout.activity_notifications appears to be unused [UnusedResources]
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_accept.xml:2: Warning: The resource R.drawable.button_accept appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\button_reject.xml:2: Warning: The resource R.drawable.button_reject appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\card_selected.xml:2: Warning: The resource R.drawable.card_selected appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\category_tag_background.xml:2: Warning: The resource R.drawable.category_tag_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:9: Warning: The resource R.color.sky_blue appears to be unused [UnusedResources]
    <color name="sky_blue">#B5DFFF</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:21: Warning: The resource R.color.ios_purple appears to be unused [UnusedResources]
    <color name="ios_purple">#AF52DE</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:22: Warning: The resource R.color.ios_pink appears to be unused [UnusedResources]
    <color name="ios_pink">#FF2D92</color>
           ~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.ios_yellow appears to be unused [UnusedResources]
    <color name="ios_yellow">#FFCC00</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:33: Warning: The resource R.color.pastel_peach appears to be unused [UnusedResources]
    <color name="pastel_peach">#FFEEE6</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:47: Warning: The resource R.color.surface appears to be unused [UnusedResources]
    <color name="surface">#FFFFFF</color>                <!-- 카드/서피스 화이트 -->
           ~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:52: Warning: The resource R.color.card_pressed appears to be unused [UnusedResources]
    <color name="card_pressed">#4DB5DFFF</color>         <!-- 파스텔 블루 30% 투명 -->
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:63: Warning: The resource R.color.text_input appears to be unused [UnusedResources]
    <color name="text_input">#000000</color>             <!-- 입력 텍스트 (블랙) -->
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:64: Warning: The resource R.color.text_input_hint appears to be unused [UnusedResources]
    <color name="text_input_hint">#8E8E93</color>        <!-- 입력 힌트 (미디엄 그레이) -->
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:73: Warning: The resource R.color.modal_background appears to be unused [UnusedResources]
    <color name="modal_background">#F2F2F7</color>       <!-- iOS 모달 배경 -->
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:77: Warning: The resource R.color.route_card_background appears to be unused [UnusedResources]
    <color name="route_card_background">@color/card_background</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:78: Warning: The resource R.color.route_card_selected appears to be unused [UnusedResources]
    <color name="route_card_selected">@color/card_selected</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:88: Warning: The resource R.color.background_tertiary appears to be unused [UnusedResources]
    <color name="background_tertiary">#FFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:93: Warning: The resource R.color.purple_700 appears to be unused [UnusedResources]
    <color name="purple_700">#8E44AD</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:100: Warning: The resource R.color.divider appears to be unused [UnusedResources]
    <color name="divider">#E5E5EA</color>
           ~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:105: Warning: The resource R.color.ios_green_light appears to be unused [UnusedResources]
    <color name="ios_green_light">#E8F8F5</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:106: Warning: The resource R.color.ios_red_light appears to be unused [UnusedResources]
    <color name="ios_red_light">#FFE4E6</color>
           ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:107: Warning: The resource R.color.ios_orange_light appears to be unused [UnusedResources]
    <color name="ios_orange_light">#FFF8DC</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\colors.xml:108: Warning: The resource R.color.ios_purple_light appears to be unused [UnusedResources]
    <color name="ios_purple_light">#F0F0FF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail.xml:2: Warning: The resource R.layout.dialog_schedule_detail appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:2: Warning: The resource R.layout.dialog_schedule_detail_improved appears to be unused [UnusedResources]
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\values\dimens.xml:12: Warning: The resource R.dimen.card_spacing appears to be unused [UnusedResources]
    <dimen name="card_spacing">8dp</dimen>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\dimens.xml:16: Warning: The resource R.dimen.route_card_corner_radius appears to be unused [UnusedResources]
    <dimen name="route_card_corner_radius">@dimen/card_corner_radius</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\dimens.xml:17: Warning: The resource R.dimen.route_card_elevation appears to be unused [UnusedResources]
    <dimen name="route_card_elevation">@dimen/card_elevation</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\dimens.xml:18: Warning: The resource R.dimen.route_card_margin appears to be unused [UnusedResources]
    <dimen name="route_card_margin">@dimen/card_margin</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\dimens.xml:20: Warning: The resource R.dimen.route_card_spacing appears to be unused [UnusedResources]
    <dimen name="route_card_spacing">@dimen/card_spacing</dimen>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_arrow_right.xml:2: Warning: The resource R.drawable.ic_arrow_right appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_cancel.xml:1: Warning: The resource R.drawable.ic_cancel appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_check_circle.xml:1: Warning: The resource R.drawable.ic_check_circle appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_directions_car.xml:2: Warning: The resource R.drawable.ic_directions_car appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_directions_transit.xml:2: Warning: The resource R.drawable.ic_directions_transit appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_directions_walk.xml:2: Warning: The resource R.drawable.ic_directions_walk appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_driving.xml:2: Warning: The resource R.drawable.ic_driving appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_transit.xml:2: Warning: The resource R.drawable.ic_transit appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ic_walking.xml:2: Warning: The resource R.drawable.ic_walking appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\indicator_dot_active.xml:2: Warning: The resource R.drawable.indicator_dot_active appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\indicator_dot_inactive.xml:2: Warning: The resource R.drawable.indicator_dot_inactive appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_badge_background.xml:2: Warning: The resource R.drawable.ios_badge_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_header_blue_gradient.xml:2: Warning: The resource R.drawable.ios_header_blue_gradient appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_tab_container.xml:2: Warning: The resource R.drawable.ios_tab_container appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_tab_selected.xml:2: Warning: The resource R.drawable.ios_tab_selected appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\ios_tab_unselected.xml:2: Warning: The resource R.drawable.ios_tab_unselected appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_home_schedule.xml:2: Warning: The resource R.layout.item_home_schedule appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggest.xml:2: Warning: The resource R.layout.item_place_suggest appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:2: Warning: The resource R.layout.item_route_option appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_header.xml:2: Warning: The resource R.layout.item_schedule_header appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:2: Warning: The resource R.layout.item_schedule_list appears to be unused [UnusedResources]
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\notification_badge_background.xml:2: Warning: The resource R.drawable.notification_badge_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\recommended_badge.xml:2: Warning: The resource R.drawable.recommended_badge appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\route_normal_background.xml:2: Warning: The resource R.drawable.route_normal_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\route_recommended_background.xml:2: Warning: The resource R.drawable.route_recommended_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\schedule_info_background.xml:2: Warning: The resource R.drawable.schedule_info_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\status_badge_background.xml:2: Warning: The resource R.drawable.status_badge_background appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\Users\<USER>\TimeMate\app\src\main\res\values\styles.xml:112: Warning: The resource R.style.iOSEditText appears to be unused [UnusedResources]
    <style name="iOSEditText">
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\styles.xml:136: Warning: The resource R.style.TimeMateCard appears to be unused [UnusedResources]
    <style name="TimeMateCard">
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\styles.xml:180: Warning: The resource R.style.TimeMateEditText appears to be unused [UnusedResources]
    <style name="TimeMateEditText">
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\styles.xml:216: Warning: The resource R.style.CalendarHeaderTextAppearance appears to be unused [UnusedResources]
    <style name="CalendarHeaderTextAppearance">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\themes.xml:3: Warning: The resource R.style.Base_Theme_TimeMate appears to be unused [UnusedResources]
    <style name="Base.Theme.TimeMate" parent="Theme.Material3.DayNight.NoActionBar">
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\values\themes.xml:8: Warning: The resource R.style.Theme_TimeMate appears to be unused [UnusedResources]
    <style name="Theme.TimeMate" parent="Base.Theme.TimeMate" />
           ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\drawable\weather_card_background.xml:2: Warning: The resource R.drawable.weather_card_background appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:76: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
                        <LinearLayout
                         ~~~~~~~~~~~~

   Explanation for issues of type "UselessParent":
   A layout with children that has no siblings, is not a scrollview or a root
   layout, and does not have a background, can be removed and have its
   children moved directly into the parent for a flatter and more efficient
   layout hierarchy.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:165: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:83: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:99: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:111: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail.xml:74: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:177: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:87: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:99: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:7: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:72: Warning: This text field does not specify an inputType [TextFields]
                <EditText
                 ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:12: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:51: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend.xml:99: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend.xml:110: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:48: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                android:textSize="10sp"
                ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:117: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:167: Warning: Missing autofillHints attribute [Autofill]
                    <EditText
                     ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:7: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:13: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:72: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:332: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:12: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:18: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:25: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:51: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:57: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\Users\<USER>\TimeMate\app\build.gradle.kts:67: Warning: Use version catalog instead [UseTomlInstead]
    annotationProcessor("androidx.room:room-compiler:2.6.1")
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:68: Warning: Use version catalog instead [UseTomlInstead]
    implementation ("com.kakao.sdk:v2-user:2.19.0")
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:71: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:72: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:retrofit:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:73: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.retrofit2:converter-gson:2.9.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:74: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:77: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.github.bumptech.glide:glide:4.16.0")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:78: Warning: Use version catalog instead [UseTomlInstead]
    annotationProcessor("com.github.bumptech.glide:compiler:4.16.0")
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:81: Warning: Use version catalog instead [UseTomlInstead]
    implementation("org.jsoup:jsoup:1.16.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:84: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.code.gson:gson:2.10.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\build.gradle.kts:87: Warning: Use version catalog instead [UseTomlInstead]
    implementation("androidx.work:work-runtime:2.8.1")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\CalendarView.java:190: Warning: Custom view CalendarView overrides onTouchEvent but not performClick [ClickableViewAccessibility]
    public boolean onTouchEvent(MotionEvent event) {
                   ~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:582: Warning: onTouch lambda should call View#performClick when a click is detected [ClickableViewAccessibility]
            rootView.setOnTouchListener((v, event) -> {
                                        ^

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:19: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageButton
         ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:65: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:46: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageButton
                     ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:229: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:260: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:321: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:402: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notification.xml:18: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageButton
         ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:22: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:66: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:18: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageButton
         ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageButton
         ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:25: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:44: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:72: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:97: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:132: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:168: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:59: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageButton
             ~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_account.xml:27: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_account.xml:82: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend.xml:23: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_home_schedule.xml:62: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_notification.xml:26: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_ootd_recommendation.xml:22: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggest.xml:19: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggestion.xml:12: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggestion.xml:65: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:30: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:163: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:177: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:22: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:61: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:84: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:113: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:36: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:72: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:90: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:106: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:113: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:25: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:64: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\AccountAdapter.java:68: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textUserId.setText("ID: " + user.userId);
                               ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\AccountAdapter.java:68: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            textUserId.setText("ID: " + user.userId);
                               ~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\AccountAdapter.java:83: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textCreatedDate.setText("가입일: " + createdDate);
                                    ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\AccountSwitchActivity.java:127: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    textCurrentUser.setText("현재 로그인: " + currentUser);
                                            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\dialog\DirectionsBottomSheetDialog.java:145: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textRouteHeader.setText(departure + " → " + destination);
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\dialog\DirectionsBottomSheetDialog.java:146: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textRouteCount.setText(routes.size() + "개 경로");
                               ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\friend\adapter\FriendListAdapter.java:93: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textUserId.setText("@" + friend.friendUserId);
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\adapters\FriendSelectionAdapter.java:75: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textFriendId.setText("@" + friend.friendUserId);
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\adapter\ImprovedScheduleAdapter.java:174: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        textMemo.setText(currentMemo + "\n\n" + routeDisplay.toString());
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\adapters\OOTDRecommendationAdapter.java:96: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    textOOTDTags.setText("#" + recommendation.tags.replace(",", " #"));
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\recommendation\PlaceWithImageAdapter.java:130: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textDistance.setText(place.getDistance() + "m");
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\recommendation\RecommendationActivity.java:1877: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        textResultCount.setText(places.size() + "개 장소");
                                                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ui\recommendation\RecommendationAdapter.java:108: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textPlaceDistance.setText("📍 " + place.distance);
                                      ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1111: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textSelectedDateTime.setText(dateStr + " " + timeStr);
                                     ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1934: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textDistance.setText("📏 거리: " + result.getDistance());
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1939: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textDuration.setText("⏱️ 소요시간: " + result.getDuration());
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1944: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textTollFare.setText("💰 통행료: " + result.getTollFare());
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:1949: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textFuelPrice.setText("⛽ 연료비: " + result.getFuelPrice());
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2468: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textSelectedCount.setText(selectedCount + "명 선택됨");
                                          ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\ScheduleAddActivity.java:2473: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textSelectedCount.setText(selectedFriends.size() + "명 선택됨");
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\ScheduleDetailAdapter.java:104: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textDeparture.setText("출발: " + schedule.departure);
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\ScheduleDetailAdapter.java:105: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textDestination.setText("도착: " + schedule.destination);
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\schedule\adapter\ScheduleListAdapter.java:101: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                textLocation.setText(schedule.departure + " → " + schedule.destination);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminderDetailActivity.java:164: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textRoute.setText(reminder.departure + " → " + reminder.destination);
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminderDetailActivity.java:165: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textDuration.setText("예상 " + reminder.durationMinutes + "분 소요");
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminderDetailActivity.java:166: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textDepartureTime.setText("추천 출발시간: " + reminder.recommendedDepartureTime);
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminderDetailActivity.java:167: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textTransport.setText("교통수단: " + reminder.getTransportDisplayName());
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminderDetailActivity.java:168: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        textDistance.setText("거리: " + reminder.distance);
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminderDetailActivity.java:172: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textTollFare.setText("통행료: " + reminder.tollFare);
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\ScheduleReminderDetailActivity.java:173: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textFuelPrice.setText("연료비: " + reminder.fuelPrice);
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\TomorrowReminderAdapter.java:149: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textRoute.setText(reminder.departure + " → " + reminder.destination);
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\TomorrowReminderAdapter.java:152: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textDuration.setText("예상 " + reminder.durationMinutes + "분 소요");
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\java\com\example\timemate\features\home\adapter\TomorrowReminderAdapter.java:155: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            textDepartureTime.setText("추천 출발: " + reminder.recommendedDepartureTime);
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:20: Warning: Hardcoded string "계정 전환", should use @string resource [HardcodedText]
            android:text="계정 전환"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:28: Warning: Hardcoded string "사용할 계정을 선택하거나 새 계정을 만드세요n계정별로 완전히 분리된 데이터를 가집니다", should use @string resource [HardcodedText]
            android:text="사용할 계정을 선택하거나 새 계정을 만드세요\n계정별로 완전히 분리된 데이터를 가집니다"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:54: Warning: Hardcoded string "🔐 현재 계정", should use @string resource [HardcodedText]
                android:text="🔐 현재 계정"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:64: Warning: Hardcoded string "로그인되지 않음", should use @string resource [HardcodedText]
                android:text="로그인되지 않음"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:73: Warning: Hardcoded string "현재 계정으로 계속", should use @string resource [HardcodedText]
                android:text="현재 계정으로 계속"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:91: Warning: Hardcoded string "👥 다른 계정", should use @string resource [HardcodedText]
            android:text="👥 다른 계정"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_account_switch.xml:120: Warning: Hardcoded string "새 계정 만들기", should use @string resource [HardcodedText]
            android:text="새 계정 만들기"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:32: Warning: Hardcoded string "친구 추가", should use @string resource [HardcodedText]
            android:text="친구 추가"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:77: Warning: Hardcoded string "새로운 친구를 추가해보세요!", should use @string resource [HardcodedText]
                    android:text="새로운 친구를 추가해보세요!"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:86: Warning: Hardcoded string "친구와 함께 일정을 공유하고 관리하세요", should use @string resource [HardcodedText]
                    android:text="친구와 함께 일정을 공유하고 관리하세요"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:111: Warning: Hardcoded string "👤 친구 ID", should use @string resource [HardcodedText]
                        android:text="👤 친구 ID"
                        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:121: Warning: Hardcoded string "친구 ID 입력", should use @string resource [HardcodedText]
                        android:hint="친구 ID 입력"
                        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:161: Warning: Hardcoded string "✏️ 친구 닉네임", should use @string resource [HardcodedText]
                        android:text="✏️ 친구 닉네임"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:171: Warning: Hardcoded string "닉네임 입력", should use @string resource [HardcodedText]
                        android:hint="닉네임 입력"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:205: Warning: Hardcoded string "💡 친구의 사용자 ID 찾는 방법:nn1. 친구에게 개인정보 화면을 열어달라고 요청n2. 사용자 ID를 클릭하면 자동으로 복사됩니다n3. 복사된 ID를 위에 입력하세요!", should use @string resource [HardcodedText]
                    android:text="💡 친구의 사용자 ID 찾는 방법:\n\n1. 친구에게 개인정보 화면을 열어달라고 요청\n2. 사용자 ID를 클릭하면 자동으로 복사됩니다\n3. 복사된 ID를 위에 입력하세요!"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_add.xml:218: Warning: Hardcoded string "👥 친구 추가", should use @string resource [HardcodedText]
                android:text="👥 친구 추가"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml:26: Warning: Hardcoded string "친구 목록", should use @string resource [HardcodedText]
            android:text="친구 목록"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml:53: Warning: Hardcoded string "등록된 친구", should use @string resource [HardcodedText]
            android:text="등록된 친구"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml:78: Warning: Hardcoded string "👥", should use @string resource [HardcodedText]
                android:text="👥"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml:85: Warning: Hardcoded string "아직 친구가 없습니다", should use @string resource [HardcodedText]
                android:text="아직 친구가 없습니다"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml:95: Warning: Hardcoded string "+ 버튼을 눌러 친구를 추가해보세요", should use @string resource [HardcodedText]
                android:text="+ 버튼을 눌러 친구를 추가해보세요"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_friend_list.xml:125: Warning: Hardcoded string "친구 추가", should use @string resource [HardcodedText]
        android:contentDescription="친구 추가"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:42: Warning: Hardcoded string "TimeMate", should use @string resource [HardcodedText]
                        android:text="TimeMate"
                        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:86: Warning: Hardcoded string "--°", should use @string resource [HardcodedText]
                                android:text="--°"
                                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:99: Warning: Hardcoded string "위치 확인 중...", should use @string resource [HardcodedText]
                                    android:text="위치 확인 중..."
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:107: Warning: Hardcoded string "날씨 정보 로딩 중...", should use @string resource [HardcodedText]
                                    android:text="날씨 정보 로딩 중..."
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:126: Warning: Hardcoded string "체감 --°", should use @string resource [HardcodedText]
                            android:text="체감 --°"
                            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:133: Warning: Hardcoded string "습도 --%", should use @string resource [HardcodedText]
                            android:text="습도 --%"
                            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:165: Warning: Hardcoded string "오늘의 일정", should use @string resource [HardcodedText]
                        android:text="오늘의 일정"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:178: Warning: Hardcoded string "12월 25일 (수)", should use @string resource [HardcodedText]
                        android:text="12월 25일 (수)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:197: Warning: Hardcoded string "오늘 예정된 일정이 없습니다 ✨", should use @string resource [HardcodedText]
                    android:text="오늘 예정된 일정이 없습니다 ✨"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:239: Warning: Hardcoded string "일정 추가", should use @string resource [HardcodedText]
                        android:text="일정 추가"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:270: Warning: Hardcoded string "일정 보기", should use @string resource [HardcodedText]
                        android:text="일정 보기"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:331: Warning: Hardcoded string "내일 일정 미리보기", should use @string resource [HardcodedText]
                            android:text="내일 일정 미리보기"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:341: Warning: Hardcoded string "회의 참석", should use @string resource [HardcodedText]
                        android:text="회의 참석"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:350: Warning: Hardcoded string "집 → 회사", should use @string resource [HardcodedText]
                        android:text="집 → 회사"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:365: Warning: Hardcoded string "시간: 09:00", should use @string resource [HardcodedText]
                            android:text="시간: 09:00"
                            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:374: Warning: Hardcoded string "내일 일정", should use @string resource [HardcodedText]
                            android:text="내일 일정"
                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:412: Warning: Hardcoded string "오늘의 OOTD 추천", should use @string resource [HardcodedText]
                        android:text="오늘의 OOTD 추천"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_home.xml:422: Warning: Hardcoded string "현재 날씨에 맞는 옷차림을 추천해드려요", should use @string resource [HardcodedText]
                    android:text="현재 날씨에 맞는 옷차림을 추천해드려요"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:9: Warning: Hardcoded string "아이디", should use @string resource [HardcodedText]
        android:hint="아이디"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:15: Warning: Hardcoded string "비밀번호", should use @string resource [HardcodedText]
        android:hint="비밀번호"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:23: Warning: Hardcoded string "로그인", should use @string resource [HardcodedText]
        android:text="로그인"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:33: Warning: Hardcoded string "🔐 비밀번호를 잊으셨나요?", should use @string resource [HardcodedText]
        android:text="🔐 비밀번호를 잊으셨나요?"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:53: Warning: Hardcoded string "취소", should use @string resource [HardcodedText]
            android:text="취소"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_manual_login.xml:64: Warning: Hardcoded string "회원가입", should use @string resource [HardcodedText]
            android:text="회원가입"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notification.xml:30: Warning: Hardcoded string "알림함", should use @string resource [HardcodedText]
            android:text="알림함"
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notification.xml:70: Warning: Hardcoded string "🔕", should use @string resource [HardcodedText]
                android:text="🔕"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notification.xml:77: Warning: Hardcoded string "새로운 알림이 없습니다", should use @string resource [HardcodedText]
                android:text="새로운 알림이 없습니다"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notification.xml:86: Warning: Hardcoded string "친구 초대나 일정 공유 요청이 있으면n여기에 표시됩니다", should use @string resource [HardcodedText]
                android:text="친구 초대나 일정 공유 요청이 있으면\n여기에 표시됩니다"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:35: Warning: Hardcoded string "일정 초대 알림", should use @string resource [HardcodedText]
                android:text="일정 초대 알림"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:44: Warning: Hardcoded string "0개", should use @string resource [HardcodedText]
                android:text="0개"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:64: Warning: Hardcoded string "대기 중", should use @string resource [HardcodedText]
                android:text="대기 중"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:74: Warning: Hardcoded string "전체", should use @string resource [HardcodedText]
                android:text="전체"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:109: Warning: Hardcoded string "📬", should use @string resource [HardcodedText]
                    android:text="📬"
                    ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:116: Warning: Hardcoded string "받은 초대가 없습니다", should use @string resource [HardcodedText]
                    android:text="받은 초대가 없습니다"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_notifications.xml:124: Warning: Hardcoded string "친구들이 일정을 공유하면 여기에 표시됩니다", should use @string resource [HardcodedText]
                    android:text="친구들이 일정을 공유하면 여기에 표시됩니다"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:28: Warning: Hardcoded string "🔐", should use @string resource [HardcodedText]
                android:text="🔐"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:36: Warning: Hardcoded string "비밀번호 찾기", should use @string resource [HardcodedText]
                android:text="비밀번호 찾기"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:46: Warning: Hardcoded string "아이디를 입력하여 비밀번호를 재설정하세요", should use @string resource [HardcodedText]
                android:text="아이디를 입력하여 비밀번호를 재설정하세요"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:64: Warning: Hardcoded string "아이디", should use @string resource [HardcodedText]
                android:hint="아이디"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:82: Warning: Hardcoded string "새 비밀번호", should use @string resource [HardcodedText]
                android:hint="새 비밀번호"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:101: Warning: Hardcoded string "새 비밀번호 확인", should use @string resource [HardcodedText]
                android:hint="새 비밀번호 확인"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:131: Warning: Hardcoded string "📋 사용자 정보 확인", should use @string resource [HardcodedText]
                android:text="📋 사용자 정보 확인"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:156: Warning: Hardcoded string "취소", should use @string resource [HardcodedText]
                android:text="취소"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_password_reset.xml:167: Warning: Hardcoded string "사용자 확인", should use @string resource [HardcodedText]
                android:text="사용자 확인"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:26: Warning: Hardcoded string "뒤로가기", should use @string resource [HardcodedText]
            android:contentDescription="뒤로가기"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:33: Warning: Hardcoded string "프로필", should use @string resource [HardcodedText]
            android:text="프로필"
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:78: Warning: Hardcoded string "개인정보", should use @string resource [HardcodedText]
            android:text="개인정보"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:98: Warning: Hardcoded string "사용자 정보", should use @string resource [HardcodedText]
            android:text="사용자 정보"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:112: Warning: Hardcoded string "이름:", should use @string resource [HardcodedText]
                android:text="이름:"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:121: Warning: Hardcoded string "사용자", should use @string resource [HardcodedText]
                android:text="사용자"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:137: Warning: Hardcoded string "사용자 ID:", should use @string resource [HardcodedText]
                android:text="사용자 ID:"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:146: Warning: Hardcoded string "user123", should use @string resource [HardcodedText]
                android:text="user123"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:166: Warning: Hardcoded string "이메일:", should use @string resource [HardcodedText]
                android:text="이메일:"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:175: Warning: Hardcoded string "이메일 없음", should use @string resource [HardcodedText]
                android:text="이메일 없음"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:197: Warning: Hardcoded string "계정 관리", should use @string resource [HardcodedText]
            android:text="계정 관리"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:207: Warning: Hardcoded string "🔄 계정 전환", should use @string resource [HardcodedText]
            android:text="🔄 계정 전환"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:215: Warning: Hardcoded string "로그아웃", should use @string resource [HardcodedText]
            android:text="로그아웃"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:223: Warning: Hardcoded string "계정 삭제", should use @string resource [HardcodedText]
            android:text="계정 삭제"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:242: Warning: Hardcoded string "경로 설정", should use @string resource [HardcodedText]
            android:text="경로 설정"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:257: Warning: Hardcoded string "우선순위:", should use @string resource [HardcodedText]
                android:text="우선순위:"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:273: Warning: Hardcoded string "시간 우선", should use @string resource [HardcodedText]
                    android:text="시간 우선"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:282: Warning: Hardcoded string "비용 우선", should use @string resource [HardcodedText]
                    android:text="비용 우선"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:298: Warning: Hardcoded string "실시간 정보:", should use @string resource [HardcodedText]
                android:text="실시간 정보:"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_profile.xml:308: Warning: Hardcoded string "교통상황 반영", should use @string resource [HardcodedText]
                android:text="교통상황 반영"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:32: Warning: Hardcoded string "추천 장소", should use @string resource [HardcodedText]
                    android:text="추천 장소"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:60: Warning: Hardcoded string "📍 검색 위치", should use @string resource [HardcodedText]
                    android:text="📍 검색 위치"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:69: Warning: Hardcoded string "지역을 입력하세요", should use @string resource [HardcodedText]
                    android:hint="지역을 입력하세요"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:92: Warning: Hardcoded string "🏷️ 카테고리", should use @string resource [HardcodedText]
                    android:text="🏷️ 카테고리"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:109: Warning: Hardcoded string "🍽️ 맛집", should use @string resource [HardcodedText]
                        android:text="🍽️ 맛집"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:130: Warning: Hardcoded string "☕ 카페", should use @string resource [HardcodedText]
                        android:text="☕ 카페"
                        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:151: Warning: Hardcoded string "🎯 놀거리", should use @string resource [HardcodedText]
                        android:text="🎯 놀거리"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:180: Warning: Hardcoded string "🏨 숙소", should use @string resource [HardcodedText]
                        android:text="🏨 숙소"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:209: Warning: Hardcoded string "🔍 주변 장소 검색", should use @string resource [HardcodedText]
                    android:text="🔍 주변 장소 검색"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:253: Warning: Hardcoded string "🗺️ 지도 보기", should use @string resource [HardcodedText]
                            android:text="🗺️ 지도 보기"
                            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:263: Warning: Hardcoded string "확대", should use @string resource [HardcodedText]
                            android:text="확대"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:298: Warning: Hardcoded string "지도 로딩 중...", should use @string resource [HardcodedText]
                                android:text="지도 로딩 중..."
                                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:341: Warning: Hardcoded string "🎯 추천 결과", should use @string resource [HardcodedText]
                            android:text="🎯 추천 결과"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:351: Warning: Hardcoded string "0개 장소", should use @string resource [HardcodedText]
                            android:text="0개 장소"
                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:394: Warning: Hardcoded string "🔍", should use @string resource [HardcodedText]
                        android:text="🔍"
                        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:401: Warning: Hardcoded string "위치를 입력하고 검색해보세요!", should use @string resource [HardcodedText]
                        android:text="위치를 입력하고 검색해보세요!"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_recommendation.xml:412: Warning: Hardcoded string "주변의 맛집, 카페, 놀거리를 찾아드려요", should use @string resource [HardcodedText]
                        android:text="주변의 맛집, 카페, 놀거리를 찾아드려요"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:26: Warning: Hardcoded string "뒤로가기", should use @string resource [HardcodedText]
            android:contentDescription="뒤로가기" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:32: Warning: Hardcoded string "새 일정 추가", should use @string resource [HardcodedText]
            android:text="새 일정 추가"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:67: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
                    android:text="일정 제목"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:76: Warning: Hardcoded string "예: 친구와 카페 가기", should use @string resource [HardcodedText]
                    android:hint="예: 친구와 카페 가기"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:97: Warning: Hardcoded string "날짜 및 시간", should use @string resource [HardcodedText]
                    android:text="날짜 및 시간"
                    ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:112: Warning: Hardcoded string "날짜 선택", should use @string resource [HardcodedText]
                        android:text="날짜 선택"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:121: Warning: Hardcoded string "시간 선택", should use @string resource [HardcodedText]
                        android:text="시간 선택"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:131: Warning: Hardcoded string "날짜와 시간을 선택해주세요", should use @string resource [HardcodedText]
                    android:text="날짜와 시간을 선택해주세요"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:152: Warning: Hardcoded string "출발지 → 도착지", should use @string resource [HardcodedText]
                    android:text="출발지 → 도착지"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:161: Warning: Hardcoded string "출발지를 입력하세요 (2글자 이상)", should use @string resource [HardcodedText]
                        android:hint="출발지를 입력하세요 (2글자 이상)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:190: Warning: Hardcoded string "도착지를 입력하세요 (2글자 이상)", should use @string resource [HardcodedText]
                        android:hint="도착지를 입력하세요 (2글자 이상)"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:220: Warning: Hardcoded string "길찾기", should use @string resource [HardcodedText]
                        android:text="길찾기"
                        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:247: Warning: Hardcoded string "✅ 선택된 경로", should use @string resource [HardcodedText]
                                android:text="✅ 선택된 경로"
                                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:258: Warning: Hardcoded string "경로 정보가 여기에 표시됩니다", should use @string resource [HardcodedText]
                                android:text="경로 정보가 여기에 표시됩니다"
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:290: Warning: Hardcoded string "함께할 친구", should use @string resource [HardcodedText]
                        android:text="함께할 친구"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:298: Warning: Hardcoded string "친구 선택", should use @string resource [HardcodedText]
                        android:text="친구 선택"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:307: Warning: Hardcoded string "선택된 친구가 없습니다", should use @string resource [HardcodedText]
                    android:text="선택된 친구가 없습니다"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:327: Warning: Hardcoded string "메모", should use @string resource [HardcodedText]
                    android:text="메모"
                    ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:336: Warning: Hardcoded string "일정에 대한 메모를 입력하세요", should use @string resource [HardcodedText]
                    android:hint="일정에 대한 메모를 입력하세요"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:364: Warning: Hardcoded string "취소", should use @string resource [HardcodedText]
            android:text="취소"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_add.xml:373: Warning: Hardcoded string "저장", should use @string resource [HardcodedText]
            android:text="저장"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_calendar.xml:62: Warning: Hardcoded string "오늘의 일정", should use @string resource [HardcodedText]
            android:text="오늘의 일정"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_calendar.xml:93: Warning: Hardcoded string "선택한 날짜에 일정이 없습니다", should use @string resource [HardcodedText]
                android:text="선택한 날짜에 일정이 없습니다"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_calendar.xml:101: Warning: Hardcoded string "+ 버튼을 눌러 새 일정을 추가해보세요", should use @string resource [HardcodedText]
                android:text="+ 버튼을 눌러 새 일정을 추가해보세요"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_calendar.xml:118: Warning: Hardcoded string "일정 추가", should use @string resource [HardcodedText]
        android:contentDescription="일정 추가"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:27: Warning: Hardcoded string "일정 관리", should use @string resource [HardcodedText]
                android:text="일정 관리"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:57: Warning: Hardcoded string "‹", should use @string resource [HardcodedText]
                    android:text="‹"
                    ~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:69: Warning: Hardcoded string "2024년 1월", should use @string resource [HardcodedText]
                    android:text="2024년 1월"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:79: Warning: Hardcoded string "›", should use @string resource [HardcodedText]
                    android:text="›"
                    ~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:97: Warning: Hardcoded string "• 일정이 있는 날짜를 터치하면 상세 정보를 볼 수 있습니다", should use @string resource [HardcodedText]
                android:text="• 일정이 있는 날짜를 터치하면 상세 정보를 볼 수 있습니다"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:134: Warning: Hardcoded string "등록된 일정이 없습니다", should use @string resource [HardcodedText]
                    android:text="등록된 일정이 없습니다"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:145: Warning: Hardcoded string "+ 버튼을 눌러 일정을 추가해보세요", should use @string resource [HardcodedText]
                    android:text="+ 버튼을 눌러 일정을 추가해보세요"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_list.xml:175: Warning: Hardcoded string "일정 추가", should use @string resource [HardcodedText]
        android:contentDescription="일정 추가"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:30: Warning: Hardcoded string "일정 알림", should use @string resource [HardcodedText]
            android:text="일정 알림"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:72: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
                        android:text="일정 제목"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:83: Warning: Hardcoded string "2024년 1월 1일 10:00", should use @string resource [HardcodedText]
                        android:text="2024년 1월 1일 10:00"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:93: Warning: Hardcoded string "출발지 → 도착지", should use @string resource [HardcodedText]
                        android:text="출발지 → 도착지"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:120: Warning: Hardcoded string "🚗 추천 출발 정보", should use @string resource [HardcodedText]
                        android:text="🚗 추천 출발 정보"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:130: Warning: Hardcoded string "추천 출발시간: 09:30", should use @string resource [HardcodedText]
                        android:text="추천 출발시간: 09:30"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:140: Warning: Hardcoded string "예상 30분 소요", should use @string resource [HardcodedText]
                        android:text="예상 30분 소요"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:149: Warning: Hardcoded string "교통수단: 자동차", should use @string resource [HardcodedText]
                        android:text="교통수단: 자동차"
                        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:158: Warning: Hardcoded string "거리: 15.2 km", should use @string resource [HardcodedText]
                        android:text="거리: 15.2 km"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:184: Warning: Hardcoded string "💰 예상 비용", should use @string resource [HardcodedText]
                        android:text="💰 예상 비용"
                        ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:200: Warning: Hardcoded string "통행료: 2,500원", should use @string resource [HardcodedText]
                            android:text="통행료: 2,500원"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:209: Warning: Hardcoded string "연료비: 3,200원", should use @string resource [HardcodedText]
                            android:text="연료비: 3,200원"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:237: Warning: Hardcoded string "🗺️ 길찾기 시작", should use @string resource [HardcodedText]
            android:text="🗺️ 길찾기 시작"
            ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:255: Warning: Hardcoded string "⏰ 10분 후", should use @string resource [HardcodedText]
                android:text="⏰ 10분 후"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_schedule_reminder_detail.xml:266: Warning: Hardcoded string "✓ 확인", should use @string resource [HardcodedText]
                android:text="✓ 확인"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:14: Warning: Hardcoded string "이름", should use @string resource [HardcodedText]
            android:hint="이름"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:20: Warning: Hardcoded string "이메일", should use @string resource [HardcodedText]
            android:hint="이메일"
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:27: Warning: Hardcoded string "전화번호", should use @string resource [HardcodedText]
            android:hint="전화번호"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:40: Warning: Hardcoded string "남", should use @string resource [HardcodedText]
                android:text="남"
                ~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:46: Warning: Hardcoded string "여", should use @string resource [HardcodedText]
                android:text="여"
                ~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:53: Warning: Hardcoded string "로그인 ID", should use @string resource [HardcodedText]
            android:hint="로그인 ID"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:59: Warning: Hardcoded string "비밀번호", should use @string resource [HardcodedText]
            android:hint="비밀번호"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:74: Warning: Hardcoded string "취소", should use @string resource [HardcodedText]
                android:text="취소"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\activity_signup_form.xml:85: Warning: Hardcoded string "회원가입", should use @string resource [HardcodedText]
                android:text="회원가입"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\menu\bottom_nav_menu.xml:5: Warning: Hardcoded string "홈", should use @string resource [HardcodedText]
        android:title="홈"
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\menu\bottom_nav_menu.xml:9: Warning: Hardcoded string "일정", should use @string resource [HardcodedText]
        android:title="일정"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\menu\bottom_nav_menu.xml:13: Warning: Hardcoded string "친구목록", should use @string resource [HardcodedText]
        android:title="친구목록"
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\menu\bottom_nav_menu.xml:17: Warning: Hardcoded string "추천", should use @string resource [HardcodedText]
        android:title="추천"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\menu\bottom_nav_menu.xml:21: Warning: Hardcoded string "프로필", should use @string resource [HardcodedText]
        android:title="프로필"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:31: Warning: Hardcoded string "경로 선택", should use @string resource [HardcodedText]
            android:text="경로 선택"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:44: Warning: Hardcoded string "닫기", should use @string resource [HardcodedText]
            android:contentDescription="닫기" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:62: Warning: Hardcoded string "서울역 → 강남역", should use @string resource [HardcodedText]
            android:text="서울역 → 강남역"
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:72: Warning: Hardcoded string "3개 경로", should use @string resource [HardcodedText]
            android:text="3개 경로"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:104: Warning: Hardcoded string "취소", should use @string resource [HardcodedText]
            android:text="취소"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_directions_bottom_sheet.xml:116: Warning: Hardcoded string "일정에 저장", should use @string resource [HardcodedText]
            android:text="일정에 저장"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_friend_selection.xml:28: Warning: Hardcoded string "👥 함께할 친구", should use @string resource [HardcodedText]
                android:text="👥 함께할 친구"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_friend_selection.xml:38: Warning: Hardcoded string "일정에 초대할 친구들을 선택하세요", should use @string resource [HardcodedText]
                android:text="일정에 초대할 친구들을 선택하세요"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_friend_selection.xml:60: Warning: Hardcoded string "0명 선택됨", should use @string resource [HardcodedText]
            android:text="0명 선택됨"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_friend_selection.xml:79: Warning: Hardcoded string "취소", should use @string resource [HardcodedText]
                android:text="취소"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_friend_selection.xml:91: Warning: Hardcoded string "확인", should use @string resource [HardcodedText]
                android:text="확인"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:28: Warning: Hardcoded string "🗺️ 경로 선택", should use @string resource [HardcodedText]
                android:text="🗺️ 경로 선택"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:39: Warning: Hardcoded string "출발지 → 도착지", should use @string resource [HardcodedText]
                android:text="출발지 → 도착지"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:99: Warning: Hardcoded string "🚌", should use @string resource [HardcodedText]
                            android:text="🚌"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:108: Warning: Hardcoded string "대중교통", should use @string resource [HardcodedText]
                            android:text="대중교통"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:118: Warning: Hardcoded string "⭐ 추천", should use @string resource [HardcodedText]
                            android:text="⭐ 추천"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:141: Warning: Hardcoded string "지하철/버스 (환승 포함)", should use @string resource [HardcodedText]
                            android:text="지하철/버스 (환승 포함)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:151: Warning: Hardcoded string "22분 (1,600원)", should use @string resource [HardcodedText]
                            android:text="22분 (1,600원)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:199: Warning: Hardcoded string "🚗", should use @string resource [HardcodedText]
                            android:text="🚗"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:208: Warning: Hardcoded string "자동차", should use @string resource [HardcodedText]
                            android:text="자동차"
                            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:218: Warning: Hardcoded string "⭐ 추천", should use @string resource [HardcodedText]
                            android:text="⭐ 추천"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:241: Warning: Hardcoded string "최단거리 경로", should use @string resource [HardcodedText]
                            android:text="최단거리 경로"
                            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:251: Warning: Hardcoded string "18분 (4,100원)", should use @string resource [HardcodedText]
                            android:text="18분 (4,100원)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:299: Warning: Hardcoded string "🚴", should use @string resource [HardcodedText]
                            android:text="🚴"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:308: Warning: Hardcoded string "자전거", should use @string resource [HardcodedText]
                            android:text="자전거"
                            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:318: Warning: Hardcoded string "⭐ 추천", should use @string resource [HardcodedText]
                            android:text="⭐ 추천"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:341: Warning: Hardcoded string "자전거 경로", should use @string resource [HardcodedText]
                            android:text="자전거 경로"
                            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:351: Warning: Hardcoded string "25분 (무료)", should use @string resource [HardcodedText]
                            android:text="25분 (무료)"
                            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:399: Warning: Hardcoded string "🚶", should use @string resource [HardcodedText]
                            android:text="🚶"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:408: Warning: Hardcoded string "도보", should use @string resource [HardcodedText]
                            android:text="도보"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:418: Warning: Hardcoded string "⭐ 추천", should use @string resource [HardcodedText]
                            android:text="⭐ 추천"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:441: Warning: Hardcoded string "도보 경로", should use @string resource [HardcodedText]
                            android:text="도보 경로"
                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:451: Warning: Hardcoded string "1시간 38분 (무료)", should use @string resource [HardcodedText]
                            android:text="1시간 38분 (무료)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:499: Warning: Hardcoded string "🚕", should use @string resource [HardcodedText]
                            android:text="🚕"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:508: Warning: Hardcoded string "택시", should use @string resource [HardcodedText]
                            android:text="택시"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:518: Warning: Hardcoded string "⭐ 추천", should use @string resource [HardcodedText]
                            android:text="⭐ 추천"
                            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:541: Warning: Hardcoded string "택시 경로", should use @string resource [HardcodedText]
                            android:text="택시 경로"
                            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:551: Warning: Hardcoded string "15분 (8,200원)", should use @string resource [HardcodedText]
                            android:text="15분 (8,200원)"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:581: Warning: Hardcoded string "취소", should use @string resource [HardcodedText]
                android:text="취소"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_route_options.xml:593: Warning: Hardcoded string "선택", should use @string resource [HardcodedText]
                android:text="선택"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail.xml:23: Warning: Hardcoded string "2024년 1월 15일", should use @string resource [HardcodedText]
            android:text="2024년 1월 15일"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail.xml:70: Warning: Hardcoded string "일정 추가", should use @string resource [HardcodedText]
            android:text="일정 추가"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail.xml:79: Warning: Hardcoded string "전체 보기", should use @string resource [HardcodedText]
            android:text="전체 보기"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:39: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
                android:text="일정 제목"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:83: Warning: Hardcoded string "2024년 1월 15일 14:30", should use @string resource [HardcodedText]
                    android:text="2024년 1월 15일 14:30"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:107: Warning: Hardcoded string "출발지:", should use @string resource [HardcodedText]
                    android:text="출발지:"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:118: Warning: Hardcoded string "서울역", should use @string resource [HardcodedText]
                    android:text="서울역"
                    ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:142: Warning: Hardcoded string "도착지:", should use @string resource [HardcodedText]
                    android:text="도착지:"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:153: Warning: Hardcoded string "강남역", should use @string resource [HardcodedText]
                    android:text="강남역"
                    ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:179: Warning: Hardcoded string "메모:", should use @string resource [HardcodedText]
                    android:text="메모:"
                    ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:190: Warning: Hardcoded string "중요한 회의입니다", should use @string resource [HardcodedText]
                    android:text="중요한 회의입니다"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:210: Warning: Hardcoded string "✏️ 수정", should use @string resource [HardcodedText]
                android:text="✏️ 수정"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_improved.xml:221: Warning: Hardcoded string "🗑️ 삭제", should use @string resource [HardcodedText]
                android:text="🗑️ 삭제"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:38: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
                    android:text="일정 제목"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:51: Warning: Hardcoded string "2024년 1월 15일", should use @string resource [HardcodedText]
                    android:text="2024년 1월 15일"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:96: Warning: Hardcoded string "일정 시간", should use @string resource [HardcodedText]
                        android:text="일정 시간"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:106: Warning: Hardcoded string "오후 2:00", should use @string resource [HardcodedText]
                        android:text="오후 2:00"
                        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:127: Warning: Hardcoded string "위치 정보", should use @string resource [HardcodedText]
                        android:text="위치 정보"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:143: Warning: Hardcoded string "출발", should use @string resource [HardcodedText]
                            android:text="출발"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:153: Warning: Hardcoded string "출발지", should use @string resource [HardcodedText]
                            android:text="출발지"
                            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:169: Warning: Hardcoded string "도착", should use @string resource [HardcodedText]
                            android:text="도착"
                            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:179: Warning: Hardcoded string "도착지", should use @string resource [HardcodedText]
                            android:text="도착지"
                            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:200: Warning: Hardcoded string "상태", should use @string resource [HardcodedText]
                        android:text="상태"
                        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:210: Warning: Hardcoded string "진행중", should use @string resource [HardcodedText]
                        android:text="진행중"
                        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:231: Warning: Hardcoded string "메모", should use @string resource [HardcodedText]
                        android:text="메모"
                        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:241: Warning: Hardcoded string "메모 내용", should use @string resource [HardcodedText]
                        android:text="메모 내용"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:263: Warning: Hardcoded string "선택된 경로", should use @string resource [HardcodedText]
                        android:text="선택된 경로"
                        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:273: Warning: Hardcoded string "경로 정보", should use @string resource [HardcodedText]
                        android:text="경로 정보"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:295: Warning: Hardcoded string "함께하는 친구", should use @string resource [HardcodedText]
                        android:text="함께하는 친구"
                        ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:305: Warning: Hardcoded string "친구 목록", should use @string resource [HardcodedText]
                        android:text="친구 목록"
                        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:332: Warning: Hardcoded string "수정", should use @string resource [HardcodedText]
                android:text="수정"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\dialog_schedule_detail_ios.xml:347: Warning: Hardcoded string "삭제", should use @string resource [HardcodedText]
                android:text="삭제"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_account.xml:47: Warning: Hardcoded string "사용자 닉네임", should use @string resource [HardcodedText]
                android:text="사용자 닉네임"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_account.xml:57: Warning: Hardcoded string "ID: user123", should use @string resource [HardcodedText]
                android:text="ID: user123"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_account.xml:66: Warning: Hardcoded string "<EMAIL>", should use @string resource [HardcodedText]
                android:text="<EMAIL>"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_account.xml:75: Warning: Hardcoded string "가입일: 2024.01.01", should use @string resource [HardcodedText]
                android:text="가입일: 2024.01.01"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend.xml:45: Warning: Hardcoded string "친구 이름", should use @string resource [HardcodedText]
                android:text="친구 이름"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend.xml:98: Warning: Hardcoded string "수락", should use @string resource [HardcodedText]
                    android:text="수락"
                    ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend.xml:109: Warning: Hardcoded string "거절", should use @string resource [HardcodedText]
                    android:text="거절"
                    ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend_selection.xml:25: Warning: Hardcoded string "👤", should use @string resource [HardcodedText]
            android:text="👤"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_friend_selection.xml:43: Warning: Hardcoded string "친구 이름", should use @string resource [HardcodedText]
                android:text="친구 이름"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_home_schedule.xml:33: Warning: Hardcoded string "회의 참석", should use @string resource [HardcodedText]
            android:text="회의 참석"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_home_schedule.xml:43: Warning: Hardcoded string "⏰ 09:00", should use @string resource [HardcodedText]
            android:text="⏰ 09:00"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_home_schedule.xml:52: Warning: Hardcoded string "📍 회사 회의실", should use @string resource [HardcodedText]
            android:text="📍 회사 회의실"
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_notification.xml:44: Warning: Hardcoded string "📅 일정 초대", should use @string resource [HardcodedText]
                    android:text="📅 일정 초대"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_notification.xml:53: Warning: Hardcoded string "방금 전", should use @string resource [HardcodedText]
                    android:text="방금 전"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_notification.xml:67: Warning: Hardcoded string "친구님이 일정에 초대했습니다", should use @string resource [HardcodedText]
            android:text="친구님이 일정에 초대했습니다"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_notification.xml:86: Warning: Hardcoded string "거절", should use @string resource [HardcodedText]
                android:text="거절"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_notification.xml:98: Warning: Hardcoded string "수락", should use @string resource [HardcodedText]
                android:text="수락"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_ootd_recommendation.xml:40: Warning: Hardcoded string "👗 스타일", should use @string resource [HardcodedText]
                android:text="👗 스타일"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_ootd_recommendation.xml:60: Warning: Hardcoded string "스타일 제목", should use @string resource [HardcodedText]
                android:text="스타일 제목"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_ootd_recommendation.xml:75: Warning: Hardcoded string "스타일 설명이 여기에 표시됩니다", should use @string resource [HardcodedText]
                android:text="스타일 설명이 여기에 표시됩니다"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_ootd_recommendation.xml:88: Warning: Hardcoded string "#트렌디 #편안함 #데일리", should use @string resource [HardcodedText]
                android:text="#트렌디 #편안함 #데일리"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_autocomplete.xml:14: Warning: Hardcoded string "장소명", should use @string resource [HardcodedText]
        android:text="장소명"
        ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_autocomplete.xml:27: Warning: Hardcoded string "주소", should use @string resource [HardcodedText]
        android:text="주소"
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_autocomplete.xml:39: Warning: Hardcoded string "카테고리", should use @string resource [HardcodedText]
        android:text="카테고리"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggest.xml:37: Warning: Hardcoded string "장소명", should use @string resource [HardcodedText]
                android:text="장소명"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggest.xml:49: Warning: Hardcoded string "주소", should use @string resource [HardcodedText]
                android:text="주소"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggest.xml:63: Warning: Hardcoded string "카테고리", should use @string resource [HardcodedText]
            android:text="카테고리"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggestion.xml:31: Warning: Hardcoded string "장소명", should use @string resource [HardcodedText]
            android:text="장소명"
            ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggestion.xml:44: Warning: Hardcoded string "주소", should use @string resource [HardcodedText]
            android:text="주소"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_suggestion.xml:56: Warning: Hardcoded string "카테고리", should use @string resource [HardcodedText]
            android:text="카테고리"
            ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:32: Warning: Hardcoded string "장소 이미지", should use @string resource [HardcodedText]
                android:contentDescription="장소 이미지" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:53: Warning: Hardcoded string "로딩중", should use @string resource [HardcodedText]
                    android:text="로딩중"
                    ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:81: Warning: Hardcoded string "장소명", should use @string resource [HardcodedText]
                    android:text="장소명"
                    ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:91: Warning: Hardcoded string "500m", should use @string resource [HardcodedText]
                    android:text="500m"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:107: Warning: Hardcoded string "카테고리", should use @string resource [HardcodedText]
                android:text="카테고리"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:117: Warning: Hardcoded string "주소", should use @string resource [HardcodedText]
                android:text="주소"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:129: Warning: Hardcoded string "전화번호", should use @string resource [HardcodedText]
                android:text="전화번호"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_place_with_image.xml:146: Warning: Hardcoded string "상세보기", should use @string resource [HardcodedText]
            android:contentDescription="상세보기" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:47: Warning: Hardcoded string "🏪", should use @string resource [HardcodedText]
                android:text="🏪"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:65: Warning: Hardcoded string "장소명", should use @string resource [HardcodedText]
                android:text="장소명"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:79: Warning: Hardcoded string "카테고리", should use @string resource [HardcodedText]
                android:text="카테고리"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:90: Warning: Hardcoded string "주소", should use @string resource [HardcodedText]
                android:text="주소"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:109: Warning: Hardcoded string "⭐ 4.5", should use @string resource [HardcodedText]
                    android:text="⭐ 4.5"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:122: Warning: Hardcoded string "📍 500m", should use @string resource [HardcodedText]
                    android:text="📍 500m"
                    ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_recommendation.xml:140: Warning: Hardcoded string "길찾기", should use @string resource [HardcodedText]
                    android:text="길찾기"
                    ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:44: Warning: Hardcoded string "추천 최적 경로", should use @string resource [HardcodedText]
                android:text="추천 최적 경로"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:55: Warning: Hardcoded string "교통수단", should use @string resource [HardcodedText]
                android:contentDescription="교통수단" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:72: Warning: Hardcoded string "출발지", should use @string resource [HardcodedText]
                android:text="출발지"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:83: Warning: Hardcoded string " → ", should use @string resource [HardcodedText]
                android:text=" → "
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:93: Warning: Hardcoded string "도착지", should use @string resource [HardcodedText]
                android:text="도착지"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:115: Warning: Hardcoded string "3.2 km", should use @string resource [HardcodedText]
                android:text="3.2 km"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:131: Warning: Hardcoded string "25분", should use @string resource [HardcodedText]
                android:text="25분"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:146: Warning: Hardcoded string "3,200원", should use @string resource [HardcodedText]
                android:text="3,200원"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:172: Warning: Hardcoded string "15분", should use @string resource [HardcodedText]
                android:text="15분"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_card.xml:186: Warning: Hardcoded string "10분", should use @string resource [HardcodedText]
                android:text="10분"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:41: Warning: Hardcoded string "대중교통", should use @string resource [HardcodedText]
                android:text="대중교통"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:51: Warning: Hardcoded string "지하철 + 버스 이용", should use @string resource [HardcodedText]
                android:text="지하철 + 버스 이용"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:63: Warning: Hardcoded string "추천", should use @string resource [HardcodedText]
            android:text="추천"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:90: Warning: Hardcoded string "시간", should use @string resource [HardcodedText]
                android:text="시간"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:98: Warning: Hardcoded string "25분", should use @string resource [HardcodedText]
                android:text="25분"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:124: Warning: Hardcoded string "거리", should use @string resource [HardcodedText]
                android:text="거리"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:132: Warning: Hardcoded string "3.2km", should use @string resource [HardcodedText]
                android:text="3.2km"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:158: Warning: Hardcoded string "비용", should use @string resource [HardcodedText]
                android:text="비용"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:166: Warning: Hardcoded string "1,500원", should use @string resource [HardcodedText]
                android:text="1,500원"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_route_option.xml:181: Warning: Hardcoded string "지하철과 버스를 이용한 최적 경로입니다.", should use @string resource [HardcodedText]
        android:text="지하철과 버스를 이용한 최적 경로입니다."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule.xml:34: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
                android:text="일정 제목"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule.xml:47: Warning: Hardcoded string "날짜 및 시간", should use @string resource [HardcodedText]
                android:text="날짜 및 시간"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule.xml:57: Warning: Hardcoded string "출발지 → 도착지", should use @string resource [HardcodedText]
                android:text="출발지 → 도착지"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule.xml:70: Warning: Hardcoded string "메모", should use @string resource [HardcodedText]
                android:text="메모"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule.xml:100: Warning: Hardcoded string "일정 수정", should use @string resource [HardcodedText]
                android:contentDescription="일정 수정"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:14: Warning: Hardcoded string "회의 참석", should use @string resource [HardcodedText]
        android:text="회의 참석"
        ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:39: Warning: Hardcoded string "오전 10:00", should use @string resource [HardcodedText]
            android:text="오전 10:00"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:72: Warning: Hardcoded string "출발지", should use @string resource [HardcodedText]
                android:text="출발지"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:95: Warning: Hardcoded string "도착지", should use @string resource [HardcodedText]
                android:text="도착지"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:124: Warning: Hardcoded string "함께하는 친구들", should use @string resource [HardcodedText]
            android:text="함께하는 친구들"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:142: Warning: Hardcoded string "메모", should use @string resource [HardcodedText]
            android:text="메모"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:152: Warning: Hardcoded string "일정 메모 내용", should use @string resource [HardcodedText]
            android:text="일정 메모 내용"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:173: Warning: Hardcoded string "수정", should use @string resource [HardcodedText]
            android:text="수정"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_detail.xml:182: Warning: Hardcoded string "삭제", should use @string resource [HardcodedText]
            android:text="삭제"
            ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_header.xml:15: Warning: Hardcoded string "오늘의 일정", should use @string resource [HardcodedText]
        android:text="오늘의 일정"
        ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_header.xml:23: Warning: Hardcoded string "3개", should use @string resource [HardcodedText]
        android:text="3개"
        ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:55: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
                    android:text="일정 제목"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:65: Warning: Hardcoded string "2024년 1월 1일 10:00", should use @string resource [HardcodedText]
                    android:text="2024년 1월 1일 10:00"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:102: Warning: Hardcoded string "출발지", should use @string resource [HardcodedText]
                android:text="출발지"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:125: Warning: Hardcoded string "도착지", should use @string resource [HardcodedText]
                android:text="도착지"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:136: Warning: Hardcoded string "메모 내용", should use @string resource [HardcodedText]
            android:text="메모 내용"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:157: Warning: Hardcoded string "개인 일정", should use @string resource [HardcodedText]
                android:text="개인 일정"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_improved.xml:169: Warning: Hardcoded string "길찾기", should use @string resource [HardcodedText]
                android:text="길찾기"
                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:38: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
                android:text="일정 제목"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:51: Warning: Hardcoded string "2024-01-15 09:00", should use @string resource [HardcodedText]
                android:text="2024-01-15 09:00"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:64: Warning: Hardcoded string "출발지 → 도착지", should use @string resource [HardcodedText]
                android:text="출발지 → 도착지"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:79: Warning: Hardcoded string "메모 내용", should use @string resource [HardcodedText]
                android:text="메모 내용"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_schedule_list.xml:97: Warning: Hardcoded string "편집", should use @string resource [HardcodedText]
            android:contentDescription="편집" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:36: Warning: Hardcoded string "09:00", should use @string resource [HardcodedText]
                android:text="09:00"
                ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:49: Warning: Hardcoded string "일정 제목", should use @string resource [HardcodedText]
            android:text="일정 제목"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:76: Warning: Hardcoded string "출발지 → 도착지", should use @string resource [HardcodedText]
                android:text="출발지 → 도착지"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_today_schedule.xml:90: Warning: Hardcoded string "메모 내용", should use @string resource [HardcodedText]
            android:text="메모 내용"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:23: Warning: Hardcoded string "회의 약속", should use @string resource [HardcodedText]
            android:text="회의 약속"
            ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:34: Warning: Hardcoded string "2024-01-15 14:00", should use @string resource [HardcodedText]
            android:text="2024-01-15 14:00"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:44: Warning: Hardcoded string "집 → 회사", should use @string resource [HardcodedText]
            android:text="집 → 회사"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:54: Warning: Hardcoded string "예상 25분 소요", should use @string resource [HardcodedText]
            android:text="예상 25분 소요"
            ~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:64: Warning: Hardcoded string "추천 출발: 13:25", should use @string resource [HardcodedText]
            android:text="추천 출발: 13:25"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:75: Warning: Hardcoded string "🚇 지하철", should use @string resource [HardcodedText]
            android:text="🚇 지하철"
            ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:91: Warning: Hardcoded string "상세보기", should use @string resource [HardcodedText]
                android:text="상세보기"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_card.xml:103: Warning: Hardcoded string "확인", should use @string resource [HardcodedText]
                android:text="확인"
                ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\TimeMate\app\src\main\res\layout\item_tomorrow_reminder_header.xml:12: Warning: Hardcoded string "🌅 내일 출발 추천", should use @string resource [HardcodedText]
        android:text="🌅 내일 출발 추천"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

15 errors, 713 warnings
