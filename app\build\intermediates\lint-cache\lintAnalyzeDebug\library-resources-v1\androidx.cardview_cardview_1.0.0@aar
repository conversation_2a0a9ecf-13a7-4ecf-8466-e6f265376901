http://schemas.android.com/apk/res-auto;androidx.cardview:cardview:1.0.0;$GRADLE_USER_HOME/caches/8.11.1/transforms/3b273577ec1a001e95b5541801b37991/transformed/cardview-1.0.0/res/values/values.xml,$GRADLE_USER_HOME/caches/8.11.1/transforms/3b273577ec1a001e95b5541801b37991/transformed/cardview-1.0.0/res/values-v23/values-v23.xml,+attr:contentPaddingBottom,0,V8001d075f,3e001d0795,;dimension:;cardCornerRadius,0,V8000b0227,3a000b0259,;dimension:;contentPadding,0,V8001504f1,3800150521,;dimension:;contentPaddingRight,0,V800190626,3d0019065b,;dimension:;cardUseCompatPadding,0,V800110396,3c001103ca,;boolean:;cardBackgroundColor,0,V8000901c0,39000901f1,;color:;contentPaddingTop,0,V8001b06c2,3b001b06f5,;dimension:;android\:minWidth,0,V8001f07dd,27001f07fc,;:;cardViewStyle,0,V400020037,3300020066,;reference:;cardPreventCornerOverlap,0,V800130455,400013048d,;boolean:;cardMaxElevation,0,V8000f02f4,3a000f0326,;dimension:;contentPaddingLeft,0,V800170589,3c001705bd,;dimension:;android\:minHeight,0,V800210845,2800210865,;:;cardElevation,0,V8000d028b,37000d02ba,;dimension:;+color:cardview_light_background,0,V4000400a8,3d000400e1,;"#FFFFFFFF";cardview_shadow_end_color,0,V4000500e6,3d0005011f,;"#03000000";cardview_shadow_start_color,0,V400060124,3f0006015f,;"#37000000";cardview_dark_background,0,V40003006b,3c000300a3,;"#FF424242";+dimen:cardview_compat_inset_shadow,0,V400230883,3a002308b9,;"1dp";cardview_default_radius,0,V4002508f7,3500250928,;"2dp";cardview_default_elevation,0,V4002408be,38002408f2,;"2dp";+style:Base.CardView,0,V40026092d,c002c0ac6,;Dandroid\:Widget,cardCornerRadius:@dimen/cardview_default_radius,cardElevation:@dimen/cardview_default_elevation,cardMaxElevation:@dimen/cardview_default_elevation,cardUseCompatPadding:false,cardPreventCornerOverlap:true,;CardView.Dark,0,V4002f0b0b,c00310b84,;NcardBackgroundColor:@color/cardview_dark_background,;CardView.Light,0,V400320b89,c00340c04,;NcardBackgroundColor:@color/cardview_light_background,;CardView,0,V4002d0acb,c002e0b06,;DBase.CardView,;CardView,1,V400020037,c000400c8,;DBase.CardView,cardBackgroundColor:?android\:attr/colorBackgroundFloating,;+styleable:CardView,0,V400070164,180022087e,;-cardBackgroundColor:color:-cardCornerRadius:dimension:-cardElevation:dimension:-cardMaxElevation:dimension:-cardUseCompatPadding:boolean:-cardPreventCornerOverlap:boolean:-contentPadding:dimension:-contentPaddingLeft:dimension:-contentPaddingRight:dimension:-contentPaddingTop:dimension:-contentPaddingBottom:dimension:-minWidth::-minHeight::;