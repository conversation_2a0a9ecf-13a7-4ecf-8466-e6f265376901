<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.0" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/OOTDAdapter.java"
                            line="148"
                            column="33"
                            startOffset="5185"
                            endLine="148"
                            endColumn="79"
                            endOffset="5231"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/ImprovedScheduleAdapter.java"
                            line="214"
                            column="40"
                            startOffset="8532"
                            endLine="214"
                            endColumn="89"
                            endOffset="8581"/>
                        <location id="10"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="1441"
                            column="29"
                            startOffset="57215"
                            endLine="1441"
                            endColumn="75"
                            endOffset="57261"/>
                        <location id="11"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="1463"
                            column="29"
                            startOffset="58013"
                            endLine="1463"
                            endColumn="75"
                            endOffset="58059"/>
                        <location id="12"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="1734"
                            column="41"
                            startOffset="68633"
                            endLine="1734"
                            endColumn="103"
                            endOffset="68695"/>
                        <location id="13"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="2033"
                            column="29"
                            startOffset="80333"
                            endLine="2033"
                            endColumn="75"
                            endOffset="80379"/>
                        <location id="14"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="2055"
                            column="29"
                            startOffset="81174"
                            endLine="2055"
                            endColumn="75"
                            endOffset="81220"/>
                        <location id="15"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="2077"
                            column="29"
                            startOffset="82001"
                            endLine="2077"
                            endColumn="75"
                            endOffset="82047"/>
                        <location id="2"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/ImprovedScheduleAdapter.java"
                            line="222"
                            column="43"
                            startOffset="8961"
                            endLine="222"
                            endColumn="98"
                            endOffset="9016"/>
                        <location id="3"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
                            line="253"
                            column="36"
                            startOffset="10128"
                            endLine="253"
                            endColumn="85"
                            endOffset="10177"/>
                        <location id="4"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
                            line="269"
                            column="39"
                            startOffset="10775"
                            endLine="269"
                            endColumn="94"
                            endOffset="10830"/>
                        <location id="5"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
                            line="301"
                            column="35"
                            startOffset="11952"
                            endLine="301"
                            endColumn="90"
                            endOffset="12007"/>
                        <location id="6"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="1377"
                            column="29"
                            startOffset="54700"
                            endLine="1377"
                            endColumn="75"
                            endOffset="54746"/>
                        <location id="7"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="1387"
                            column="29"
                            startOffset="55096"
                            endLine="1387"
                            endColumn="75"
                            endOffset="55142"/>
                        <location id="8"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="1397"
                            column="29"
                            startOffset="55524"
                            endLine="1397"
                            endColumn="75"
                            endOffset="55570"/>
                        <location id="9"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
                            line="1419"
                            column="29"
                            startOffset="56403"
                            endLine="1419"
                            endColumn="75"
                            endOffset="56449"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.timemate.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.background_tertiary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="88"
            column="12"
            startOffset="3924"
            endLine="88"
            endColumn="38"
            endOffset="3950"/>
        <location id="R.color.card_pressed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="52"
            column="12"
            startOffset="1999"
            endLine="52"
            endColumn="31"
            endOffset="2018"/>
        <location id="R.color.divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="100"
            column="12"
            startOffset="4350"
            endLine="100"
            endColumn="26"
            endOffset="4364"/>
        <location id="R.color.ios_green_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="105"
            column="12"
            startOffset="4530"
            endLine="105"
            endColumn="34"
            endOffset="4552"/>
        <location id="R.color.ios_orange_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="107"
            column="12"
            startOffset="4630"
            endLine="107"
            endColumn="35"
            endOffset="4653"/>
        <location id="R.color.ios_pink"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="811"
            endLine="22"
            endColumn="27"
            endOffset="826"/>
        <location id="R.color.ios_purple"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="765"
            endLine="21"
            endColumn="29"
            endOffset="782"/>
        <location id="R.color.ios_purple_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="108"
            column="12"
            startOffset="4682"
            endLine="108"
            endColumn="35"
            endOffset="4705"/>
        <location id="R.color.ios_red_light"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="106"
            column="12"
            startOffset="4581"
            endLine="106"
            endColumn="32"
            endOffset="4601"/>
        <location id="R.color.ios_yellow"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="855"
            endLine="23"
            endColumn="29"
            endOffset="872"/>
        <location id="R.color.modal_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="73"
            column="12"
            startOffset="3148"
            endLine="73"
            endColumn="35"
            endOffset="3171"/>
        <location id="R.color.pastel_peach"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="33"
            column="12"
            startOffset="1172"
            endLine="33"
            endColumn="31"
            endOffset="1191"/>
        <location id="R.color.purple_700"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="93"
            column="12"
            startOffset="4106"
            endLine="93"
            endColumn="29"
            endOffset="4123"/>
        <location id="R.color.route_card_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="77"
            column="12"
            startOffset="3325"
            endLine="77"
            endColumn="40"
            endOffset="3353"/>
        <location id="R.color.route_card_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="78"
            column="12"
            startOffset="3397"
            endLine="78"
            endColumn="38"
            endOffset="3423"/>
        <location id="R.color.sky_blue"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="262"
            endLine="9"
            endColumn="27"
            endOffset="277"/>
        <location id="R.color.surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="47"
            column="12"
            startOffset="1737"
            endLine="47"
            endColumn="26"
            endOffset="1751"/>
        <location id="R.color.text_input"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="63"
            column="12"
            startOffset="2635"
            endLine="63"
            endColumn="29"
            endOffset="2652"/>
        <location id="R.color.text_input_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="64"
            column="12"
            startOffset="2714"
            endLine="64"
            endColumn="34"
            endOffset="2736"/>
        <location id="R.dimen.card_spacing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="12"
            column="12"
            startOffset="402"
            endLine="12"
            endColumn="31"
            endOffset="421"/>
        <location id="R.dimen.route_card_corner_radius"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="16"
            column="12"
            startOffset="509"
            endLine="16"
            endColumn="43"
            endOffset="540"/>
        <location id="R.dimen.route_card_elevation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="17"
            column="12"
            startOffset="586"
            endLine="17"
            endColumn="39"
            endOffset="613"/>
        <location id="R.dimen.route_card_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="18"
            column="12"
            startOffset="655"
            endLine="18"
            endColumn="36"
            endOffset="679"/>
        <location id="R.dimen.route_card_spacing"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="20"
            column="12"
            startOffset="783"
            endLine="20"
            endColumn="37"
            endOffset="808"/>
        <location id="R.drawable.button_accept"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_accept.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="235"/>
        <location id="R.drawable.button_reject"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_reject.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="235"/>
        <location id="R.drawable.card_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/card_selected.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="9"
            endOffset="319"/>
        <location id="R.drawable.category_tag_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/category_tag_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="9"
            endOffset="345"/>
        <location id="R.drawable.ic_arrow_right"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_right.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="10"
            endOffset="393"/>
        <location id="R.drawable.ic_cancel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cancel.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="492"/>
        <location id="R.drawable.ic_check_circle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="10"
            endOffset="436"/>
        <location id="R.drawable.ic_directions_car"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_car.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="10"
            endOffset="740"/>
        <location id="R.drawable.ic_directions_transit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_transit.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="10"
            endOffset="696"/>
        <location id="R.drawable.ic_directions_walk"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_walk.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="10"
            endOffset="627"/>
        <location id="R.drawable.ic_driving"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_driving.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="10"
            endOffset="754"/>
        <location id="R.drawable.ic_transit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_transit.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="10"
            endOffset="705"/>
        <location id="R.drawable.ic_walking"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_walking.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="13"
            endColumn="10"
            endOffset="632"/>
        <location id="R.drawable.indicator_dot_active"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/indicator_dot_active.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="9"
            endOffset="271"/>
        <location id="R.drawable.indicator_dot_inactive"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/indicator_dot_inactive.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="9"
            endOffset="277"/>
        <location id="R.drawable.ios_badge_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_badge_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="8"
            endColumn="9"
            endOffset="250"/>
        <location id="R.drawable.ios_header_blue_gradient"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_header_blue_gradient.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="18"
            endColumn="9"
            endOffset="552"/>
        <location id="R.drawable.ios_tab_container"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_tab_container.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="255"/>
        <location id="R.drawable.ios_tab_selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_tab_selected.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="243"/>
        <location id="R.drawable.ios_tab_unselected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_tab_unselected.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="254"/>
        <location id="R.drawable.notification_badge_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/notification_badge_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="11"
            endColumn="9"
            endOffset="284"/>
        <location id="R.drawable.recommended_badge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/recommended_badge.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="15"
            endColumn="9"
            endOffset="384"/>
        <location id="R.drawable.route_normal_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/route_normal_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="9"
            endOffset="462"/>
        <location id="R.drawable.route_recommended_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/route_recommended_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="9"
            endOffset="479"/>
        <location id="R.drawable.schedule_info_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/schedule_info_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="294"/>
        <location id="R.drawable.status_badge_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/status_badge_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="9"
            endColumn="9"
            endOffset="295"/>
        <location id="R.drawable.weather_card_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/weather_card_background.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="21"
            endColumn="12"
            endOffset="703"/>
        <location id="R.layout.activity_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="16"
            endOffset="263"/>
        <location id="R.layout.activity_notifications"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="135"
            endColumn="55"
            endOffset="5234"/>
        <location id="R.layout.dialog_schedule_detail"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="85"
            endColumn="16"
            endOffset="2791"/>
        <location id="R.layout.dialog_schedule_detail_improved"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="233"
            endColumn="53"
            endOffset="8942"/>
        <location id="R.layout.item_home_schedule"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_home_schedule.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="68"
            endColumn="16"
            endOffset="2372"/>
        <location id="R.layout.item_place_suggest"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggest.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="81"
            endColumn="16"
            endOffset="2779"/>
        <location id="R.layout.item_route_option"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="186"
            endColumn="16"
            endOffset="6357"/>
        <location id="R.layout.item_schedule_header"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_header.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="30"
            endColumn="16"
            endOffset="1046"/>
        <location id="R.layout.item_schedule_list"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="101"
            endColumn="37"
            endOffset="3798"/>
        <location id="R.style.Base_Theme_TimeMate"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="3"
            column="12"
            startOffset="107"
            endLine="3"
            endColumn="38"
            endOffset="133"/>
        <location id="R.style.CalendarHeaderTextAppearance"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="216"
            column="12"
            startOffset="9724"
            endLine="216"
            endColumn="47"
            endOffset="9759"/>
        <location id="R.style.Theme_TimeMate"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="8"
            column="12"
            startOffset="330"
            endLine="8"
            endColumn="33"
            endOffset="351"/>
        <location id="R.style.TimeMateCard"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="136"
            column="12"
            startOffset="6526"
            endLine="136"
            endColumn="31"
            endOffset="6545"/>
        <location id="R.style.TimeMateEditText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="180"
            column="12"
            startOffset="8281"
            endLine="180"
            endColumn="35"
            endOffset="8304"/>
        <location id="R.style.iOSEditText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="112"
            column="12"
            startOffset="5364"
            endLine="112"
            endColumn="30"
            endOffset="5382"/>
        <entry
            name="model"
            string="anim[fade_in(U),fade_out(U)],animator[card_elevation_animator(U)],attr[colorOnSurface(R),actionBarSize(R),selectableItemBackgroundBorderless(R),selectableItemBackground(R)],color[bottom_nav_color(U),sky_blue_accent(U),text_tertiary(U),ios_category_text_selector(U),ios_blue(U),accent(U),white(U),primary_color(U),light_gray(U),route_accent(U),gray(U),pastel_blue(U),primary(U),route_text_secondary(U),purple_500(U),ios_gray3(U),text_secondary(U),route_success(U),route_warning(U),background_secondary(U),sky_blue_light(U),shadow_light(U),card_background(U),border_light(U),ios_blue_light(U),sky_blue_primary(U),ios_blue_dark(U),ios_gray5(U),ios_gray6(U),modal_handle(U),divider(D),sky_blue_dark(U),text_hint(U),pastel_mint(U),text_primary(U),background_primary(U),pastel_sky(U),background(U),pastel_pink(U),ios_green(U),ios_orange(U),background_light(U),sky_blue(D),ios_red(U),background_card(U),text_on_primary(U),pastel_lavender(U),pastel_yellow(U),orange(U),route_text_primary(U),success(U),error(U),card_stroke(U),green(U),red(U),black(U),dark_gray(U),ios_purple(D),ios_pink(D),ios_yellow(D),pastel_peach(D),surface(D),card_selected(U),card_pressed(D),text_input(D),text_input_hint(D),warning(U),info(U),modal_background(D),route_card_background(D),route_card_selected(D),background_tertiary(D),purple_700(D),ios_green_light(D),ios_red_light(D),ios_orange_light(D),ios_purple_light(D)],dimen[card_elevation_selected(U),card_elevation_pressed(U),card_elevation(U),modal_corner_radius(U),route_button_corner_radius(U),card_corner_radius(U),card_margin(U),modal_handle_margin(U),modal_handle_height(U),route_section_spacing(U),modal_handle_width(U),route_card_padding(U),route_item_spacing(U),route_button_height(U),route_content_spacing(U),card_height(U),card_padding(U),route_checkbox_size(U),route_detail_text_size(U),route_icon_size(U),route_title_text_size(U),route_subtitle_text_size(U),route_time_text_size(U),route_icon_small(U),card_spacing(D),route_card_corner_radius(D),route_card_elevation(D),route_card_margin(D),route_card_spacing(D)],drawable[badge_background(U),bg_tag_rounded(U),bottom_sheet_background(U),button_accept(D),button_outline(U),button_primary(U),button_primary_ios(U),button_reject(D),button_secondary_ios(U),card_completed(U),card_normal(U),card_overdue(U),card_selected(D),category_background(U),category_tag_background(D),circle_background(U),circle_background_ios(U),circle_dot(U),edit_text_background(U),ic_access_time(U),ic_add(U),ic_arrow_back(U),ic_arrow_forward(U),ic_arrow_right(D),ic_calendar(U),ic_cancel(D),ic_check(U),ic_check_circle(D),ic_close(U),ic_directions(U),ic_directions_car(D),ic_directions_transit(D),ic_directions_walk(D),ic_driving(D),ic_edit(U),ic_friends(U),ic_home(U),ic_image_error(U),ic_image_placeholder(U),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_location(U),ic_location_end(U),ic_location_on(U),ic_location_start(U),ic_map_error(U),ic_map_placeholder(U),ic_notifications(U),ic_person(U),ic_phone(U),ic_profile(U),ic_route_bus(U),ic_route_car(U),ic_route_walk(U),ic_schedule(U),ic_schedule_notification(U),ic_snooze(U),ic_time(U),ic_transit(D),ic_walking(D),ic_weather(U),indicator_dot_active(D),indicator_dot_inactive(D),ios_badge_background(D),ios_button_background(U),ios_card_background(U),ios_category_button_selector(U),ios_chip_background(U),ios_circle_button_white(U),ios_count_background(U),ios_distance_background(U),ios_header_blue_gradient(D),ios_icon_background(U),ios_image_placeholder(U),ios_navigation_button(U),ios_rating_background(U),ios_search_button(U),ios_tab_container(D),ios_tab_selected(D),ios_tab_unselected(D),modal_handle(U),notification_badge_background(D),recommended_badge(D),route_normal_background(D),route_recommended_background(D),schedule_info_background(D),status_badge_background(D),timemate_button_background(U),timemate_button_outlined(U),timemate_button_primary(U),timemate_edittext_background(U),weather_card_background(D)],font[pretendard_bold(U),pretendard_medium(U),pretendard_regular(U)],id[textCurrentUser(U),btnCurrentAccount(U),recyclerAccounts(U),btnAddAccount(U),btnBack(U),editFriendId(U),editFriendNickname(U),btnAddFriend(U),layoutHeader(U),layoutFriendList(D),bottomNavigationView(U),recyclerFriends(U),layoutEmptyState(U),fabAddFriend(U),btnNotifications(D),layoutWeather(D),textTemperature(U),textCityName(U),textWeatherDescription(U),textFeelsLike(U),textHumidity(U),layoutTodayScheduleBox(D),textTodayDate(D),recyclerTodayScheduleBox(D),textNoScheduleToday(D),btnQuickAddSchedule(U),btnViewAllSchedules(U),recyclerTodaySchedule(U),recyclerTomorrowSchedule(U),cardTomorrowReminder(D),textTomorrowTitle(D),textTomorrowRoute(D),textTomorrowDuration(D),textTomorrowDeparture(D),textOotdDescription(D),recyclerOotd(D),editUserId(U),editPassword(U),btnLogin(U),textForgotPassword(U),btnCancel(U),btnGoSignup(U),recyclerNotifications(U),textEmptyNotifications(U),textNotificationCount(D),btnTabPending(D),btnTabAll(D),layoutNewPassword(U),editNewPassword(U),layoutConfirmPassword(U),editConfirmPassword(U),layoutUserInfo(U),textUserInfo(U),btnConfirm(U),textUserName(U),textUserId(U),textUserEmail(U),btnSwitchAccount(U),btnLogout(U),btnDeleteAccount(U),radioGroupPriority(U),radioTimePriority(U),radioCostPriority(U),switchRealtimeData(U),editSearchLocation(U),btnCategoryRestaurant(U),btnCategoryCafe(U),btnCategoryAttraction(U),btnCategoryAccommodation(U),btnSearch(U),layoutMapContainer(U),btnExpandMap(D),frameMapView(D),layoutMapLoading(D),layoutResultsContainer(U),textResultCount(U),recyclerRecommendations(U),editTitle(U),btnSelectDate(U),btnSelectTime(U),textSelectedDateTime(U),editDeparture(U),rvDepSuggest(U),editDestination(U),rvDestSuggest(U),btnGetDirections(U),layoutRouteInfo(D),textRouteInfo(U),btnSelectFriends(U),textSelectedFriends(U),editMemo(U),btnSaveSchedule(U),toolbar(D),calendarView(U),textSelectedDateTitle(D),recyclerSchedules(U),fabAddSchedule(U),btnPrevMonth(U),textCurrentMonth(U),btnNextMonth(U),textEmptySchedule(D),textTitle(U),textDateTime(U),textRoute(U),textDepartureTime(U),textDuration(U),textTransport(U),textDistance(U),textTollFare(U),textFuelPrice(U),btnStartNavigation(U),btnSnooze(U),btnDismiss(U),editNickname(U),editEmail(U),editPhone(U),radioGenderGroup(U),radioMale(D),radioFemale(D),btnSignup(U),btnClose(U),textRouteHeader(U),textRouteCount(U),recyclerRoutes(U),btnSaveToSchedule(U),recyclerViewFriends(U),textSelectedCount(U),textRouteTitle(U),checkboxPublicTransport(U),textPublicRecommended(U),textPublicRoute(U),textPublicTime(U),checkboxDriving(U),textDrivingRecommended(U),textDrivingRoute(U),textDrivingTime(U),checkboxBicycle(U),textBicycleRecommended(U),textBicycleRoute(U),textBicycleTime(U),checkboxWalking(U),textWalkingRecommended(U),textWalkingRoute(U),textWalkingTime(U),checkboxTaxi(U),textTaxiRecommended(U),textTaxiRoute(U),textTaxiTime(U),textDialogDate(D),btnCloseDialog(D),viewPagerSchedules(D),layoutIndicator(D),btnAddSchedule(D),btnViewAll(D),textScheduleTitle(U),textScheduleDateTime(D),textScheduleDeparture(D),textScheduleDestination(D),layoutMemo(U),textScheduleMemo(D),btnEditSchedule(U),btnDeleteSchedule(U),textScheduleDate(U),textScheduleTime(U),cardLocationInfo(U),textDeparture(U),textDestination(U),textStatus(U),cardMemo(U),textMemo(U),cardRouteInfo(U),cardFriends(U),textFriends(U),btnEdit(U),btnDelete(U),iconAccount(U),textNickname(U),textEmail(U),textCreatedDate(U),imgProfile(U),textName(U),layoutStatus(D),viewOnlineStatus(U),layoutActions(U),btnAccept(U),btnReject(U),textFriendName(U),textFriendId(U),checkboxFriend(U),scheduleIndicator(D),textScheduleLocation(D),iconNotification(D),textNotificationTitle(U),textNotificationTime(U),textNotificationMessage(U),imgOOTD(U),textOOTDCategory(U),textOOTDTitle(U),textOOTDDescription(U),textOOTDTags(U),textPlaceName(U),textPlaceAddress(U),textPlaceCategory(U),textAddress(U),textCategory(U),imagePlace(U),layoutImageLoading(U),textPhone(U),imagePlacePhoto(U),textPlaceIcon(U),textPlaceRating(U),textPlaceDistance(U),btnNavigation(U),checkboxRoute(U),textRouteLabel(U),iconRouteType(U),textCost(U),layoutTransportDetails(U),imgTransportMode(D),textRouteType(D),textRouteSummary(D),textRecommended(D),textDescription(D),cardBackground(U),textLocation(U),checkCompleted(U),layoutLocation(U),layoutFriends(U),textSectionTitle(U),textScheduleCount(D),iconShared(U),btnDirections(U),textTime(U),btnViewDetail(U),nav_home(U),nav_schedule(U),nav_friends(U),nav_recommendation(U),nav_profile(U),design_bottom_sheet(R)],layout[activity_account_switch(U),activity_friend_add(U),activity_friend_list(U),activity_home(U),activity_main(D),activity_manual_login(U),activity_notification(U),activity_notifications(D),activity_password_reset(U),activity_profile(U),activity_recommendation(U),activity_schedule_add(U),activity_schedule_calendar(U),item_schedule(U),activity_schedule_list(U),activity_schedule_reminder_detail(U),activity_signup_form(U),dialog_directions_bottom_sheet(U),dialog_friend_selection(U),dialog_route_options(U),dialog_schedule_detail(D),dialog_schedule_detail_improved(D),dialog_schedule_detail_ios(U),item_account(U),item_friend(U),item_friend_selection(U),item_home_schedule(D),item_notification(U),item_ootd_recommendation(U),item_place_autocomplete(U),item_place_suggest(D),item_place_suggestion(U),item_place_with_image(U),item_recommendation(U),item_route_card(U),item_route_option(D),item_schedule_detail(U),item_schedule_header(D),item_schedule_improved(U),item_schedule_list(D),item_today_schedule(U),item_tomorrow_reminder_card(U),item_tomorrow_reminder_header(U)],menu[bottom_nav_menu(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U)],style[TimeMateTheme(U),TimeMateAppBar(U),TimeMateTextTitle(U),TimeMateTextBody(U),TimeMateButton(U),TimeMateBottomNavigation(U),iOSTitle(U),TimeMateTextTemperature(U),iOSSubheadline(U),iOSCallout(U),iOSFootnote(U),iOSHeadline(U),TimeMateTextSubtitle(U),TimeMateTextCaption(U),Widget_Material3_Button_OutlinedButton(R),Widget_Material3_TextInputLayout_OutlinedBox(R),TimeMateButtonOutlined(U),iOSBody(U),TimeMateButtonPrimary(U),TextAppearance_AppCompat_Title(R),CalendarDateTextAppearance(U),CalendarWeekDayTextAppearance(U),Widget_Material3_Button(R),Widget_Material3_Button_UnelevatedButton(R),Widget_Material3_Button_TextButton(R),iOSCaption(U),Theme_Material3_DayNight_NoActionBar(R),iOSEditText(D),TimeMateCard(D),TimeMateEditText(D),CalendarHeaderTextAppearance(D),BottomSheetDialogTheme(U),Theme_Material3_DayNight_BottomSheetDialog(R),BottomSheetStyle(U),Widget_Material3_BottomSheet(R),BottomSheetShapeAppearance(U),Base_Theme_TimeMate(D),Theme_TimeMate(D)],xml[data_extraction_rules(U),backup_rules(U)];2^54^55^56,7^8^9,a^b,10^4a,14^17,18^39,19^49,38^29,4c^1d,4d^45,5f^64,6d^59,6e^56,6f^5a,70^6c,71^c,73^d^57,75^e^d,76^e,77^f^58^10,79^f^58^11^d,7e^12^8,7f^12^8,81^13^9,82^14,84^3,86^3,87^3,8a^3,8b^3,8c^3,8d^3,8e^3,8f^3,90^3,91^3,92^8,93^15,95^3,96^16,97^16,99^9a,9d^3,9e^15,9f^17,a0^8,a1^3,a2^15,a3^b,a4^3,a5^18,a6^10,a7^19,a9^d,aa^e,ab^3,ac^8,ad^8,af^b,b0^17,b1^1a,b2^1b,b3^1c^1d^1e,b4^b^1f,b5^1f,b6^d,b7^1f,b9^20^21^b,ba^22,bb^23,bc^1f^b,be^21^b,bf^1a,c0^b,c2^24,c4^8,c5^d^25,c6^1b^8,c9^26^20,ca^1b^8,cb^21^27^b,cc^1d^1e,ce^ce,cf^cf,d0^d0,1c1^1a^1f1^1f2^17^1f3^28^29^d^1d,1c2^2a^80^86^29^94^c^17^1d^59^56^cc^a2^d0^27^93^2b^1f4,1c3^ce^29^db^d9^2c^17^7^1ec^1f5^5a^85^c^54^d,1c4^1a^1f1^29^1f6^b2^a1^b3^1f7^1f8^1f9^1fa^1fb^17^85^2d^89^ab^b^1fc^1f2^1f3^1fd^2e^ae^2f^7^1ec^1f5,1c6^b^17^1fe,1c7^15^86^29^17^1ec,1c8^30^31^86^b3^1fe^29^1f3^17^1fd,1c9^2c^29^17^1ff^b3^1fe,1ca^b2^86^29^80^a4^1b^8^1f4^200^32^7^1ec^1f5,1cb^2c^ce^29^1f8^cc^9b^d0^27^b4^cf^a^1f^be^1fe^17^33^b7^b^7^1ec^1f5,1cc^1a^1f1^b2^86^29^1fb^b3^b^1f8^cc^27^201^200^1f9^1fc^9e^1f3^83^9c^202^17,1cd^30^20^4^203^d^204^205^29^1ce^17^27^7^1ec,1ce^6^5a^1d^59^56^64^29^17^9^c^5^93,1cf^2c^ce^29^b3^c^1fe^17^7^1ec^1f5^85^54^34,1d0^30^8^86^35^29^17^28^36^1d^206^37^27,1d1^17^1fe,1d2^73^5b^c2^5c^5d^5e^5f^38^5^8d^60^14^79^61^62^77^d,1d3^29^17^c^1fe^1f4,1d4^29^17^c^71^1fe^1f4,1d5^b3^29^8d^17^1fe^b,1d6^1d^80^a8^c^29^8d^17^cc^89^9e^2e^9c^32^93^1fe,1d7^b^ce^d^d0^b6^8d^b3^cf^29^17^32,1d8^35^a2^29^17^27^87,1d9^6^63^5a^1d^59^56^64^81^a2^29^17^82^39^d^207^3a,1da^80^29^17^c,1db^b3^b^29^1f3^17^1fd^27^88,1dc^a1^15^29^17^208^b,1dd^1a^72^29^17^b,1de^7e^8,1df^9b^8^29^17^7f^25,1e0^9d^e^87,1e1^33^22^b3^23^9^209^29^1f8^b5^b^17^1fa^1f9^a3^87,1e2^33^bb^a0^ba^ce^29^cf^b^d0^9^bd^2f^b8^bc^1f,1e3^6^63^5a^2^1d^59^56^3b^5f^62^10^65^66^67^a6^38^68^14^69^82^6a^18^6b^a7,1e4^c5^ac^8^29^17^c4^d^25,1e5^29^ab^b^9e^2e^17^9c^32^a2^2f^1a^1fe,1e6^b^1fc^b1^27^1fd,1e7^1d^35^89^29^17^94^8^9e^3c^87^27^9c^3d^36^a2^206,1e8^7b^84^e^9d^93,1e9^7b^84^e^9d,1ea^3e^3f^75^e^76^d,1eb^3e^f,1ec^95^a8^94^9c^a4,1ed^98^99,1ee^98^99,1f0^20a^20^26^29^8^d^1a^1d^d0,1f1^20,1f2^29,1f3^17,1f4^cf^c9^29,1f5^1d,1f6^29^ce,1f7^29,1f8^29^cf,1f9^17^d0,1fa^9^d0,1fb^29^ce,1fc^29,1fd^9,200^ca^8,201^29^d0,202^ce^cb^d,204^29,205^17,209^9^d0,20b^cc^47^48^d0,20c^b3,20d^cc^27^29,20e^29,20f^210^211,211^212^213,213^57,214^20a,215^214;;;"/>
    </map>

</incidents>
