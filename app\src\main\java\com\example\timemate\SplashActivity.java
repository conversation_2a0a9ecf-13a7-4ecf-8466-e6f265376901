package com.example.timemate;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.appcompat.app.AppCompatActivity;

import com.example.timemate.features.home.HomeActivity;
import com.example.timemate.util.UserSession;

/**
 * 스플래시 화면 Activity
 * 앱 시작 시 3초간 표시되는 로딩 화면
 */
public class SplashActivity extends AppCompatActivity {

    private static final int SPLASH_DELAY = 3000; // 3초

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash);

        // 3초 후 메인 화면으로 이동
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            navigateToMainActivity();
        }, SPLASH_DELAY);
    }

    private void navigateToMainActivity() {
        try {
            // 사용자 로그인 상태 확인
            UserSession userSession = UserSession.getInstance(this);
            
            Intent intent;
            if (userSession != null && userSession.isLoggedIn()) {
                // 로그인된 상태면 홈화면으로
                intent = new Intent(this, HomeActivity.class);
            } else {
                // 로그인되지 않은 상태면 메인 액티비티로
                intent = new Intent(this, MainActivity.class);
            }
            
            startActivity(intent);
            finish(); // 스플래시 화면 종료
            
        } catch (Exception e) {
            // 오류 발생 시 기본적으로 MainActivity로 이동
            Intent intent = new Intent(this, MainActivity.class);
            startActivity(intent);
            finish();
        }
    }

    @Override
    public void onBackPressed() {
        // 스플래시 화면에서는 뒤로가기 버튼 비활성화
        // super.onBackPressed();
    }
}
