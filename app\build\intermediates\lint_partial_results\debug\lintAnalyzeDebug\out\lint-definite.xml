<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.0" type="incidents">

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Overriding method should call `super.onBackPressed`">
        <fix-data/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ManualLoginActivity.java"
            line="224"
            column="17"
            startOffset="8879"
            endLine="224"
            endColumn="30"
            endOffset="8892"/>
    </incident>

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Overriding method should call `super.onBackPressed`">
        <fix-data/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/PasswordResetActivity.java"
            line="215"
            column="17"
            startOffset="7020"
            endLine="215"
            endColumn="30"
            endOffset="7033"/>
    </incident>

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Overriding method should call `super.onBackPressed`">
        <fix-data/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/SignupFormActivity.java"
            line="170"
            column="17"
            startOffset="6116"
            endLine="170"
            endColumn="30"
            endOffset="6129"/>
    </incident>

    <incident
        id="WrongViewCast"
        severity="error"
        message="Unexpected implicit cast to `TextView`: layout tag was `LinearLayout`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/notification/NotificationActivity.java"
            line="78"
            column="13"
            startOffset="2253"
            endLine="78"
            endColumn="79"
            endOffset="2319"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.example.timemate.ui.home.HomeActivity`, was not found in the project or the libraries">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="55"
            column="33"
            startOffset="2633"
            endLine="55"
            endColumn="54"
            endOffset="2654"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.example.timemate.ui.schedule.ScheduleListActivity`, was not found in the project or the libraries">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="56"
            column="33"
            startOffset="2691"
            endLine="56"
            endColumn="66"
            endOffset="2724"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.example.timemate.ui.friend.FriendListActivity`, was not found in the project or the libraries">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="57"
            column="33"
            startOffset="2761"
            endLine="57"
            endColumn="62"
            endOffset="2790"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.example.timemate.ui.profile.ProfileActivity`, was not found in the project or the libraries">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="58"
            column="33"
            startOffset="2827"
            endLine="58"
            endColumn="60"
            endOffset="2854"/>
    </incident>

    <incident
        id="ResourceCycle"
        severity="fatal"
        message="Font `pretendard_bold` should not reference itself">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/pretendard_bold.xml"
            line="6"
            column="9"
            startOffset="198"
            endLine="6"
            endColumn="45"
            endOffset="234"/>
    </incident>

    <incident
        id="ResourceCycle"
        severity="fatal"
        message="Font `pretendard_bold` should not reference itself">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/pretendard_bold.xml"
            line="6"
            column="9"
            startOffset="198"
            endLine="6"
            endColumn="45"
            endOffset="234"/>
    </incident>

    <incident
        id="ResourceCycle"
        severity="fatal"
        message="Font `pretendard_medium` should not reference itself">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/pretendard_medium.xml"
            line="6"
            column="9"
            startOffset="198"
            endLine="6"
            endColumn="47"
            endOffset="236"/>
    </incident>

    <incident
        id="ResourceCycle"
        severity="fatal"
        message="Font `pretendard_medium` should not reference itself">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/pretendard_medium.xml"
            line="6"
            column="9"
            startOffset="198"
            endLine="6"
            endColumn="47"
            endOffset="236"/>
    </incident>

    <incident
        id="ResourceCycle"
        severity="fatal"
        message="Font `pretendard_regular` should not reference itself">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/pretendard_regular.xml"
            line="6"
            column="9"
            startOffset="198"
            endLine="6"
            endColumn="48"
            endOffset="237"/>
    </incident>

    <incident
        id="ResourceCycle"
        severity="fatal"
        message="Font `pretendard_regular` should not reference itself">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/font/pretendard_regular.xml"
            line="6"
            column="9"
            startOffset="198"
            endLine="6"
            endColumn="48"
            endOffset="237"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/core/util/DistanceCalculator.java"
            line="244"
            column="16"
            startOffset="9541"
            endLine="244"
            endColumn="48"
            endOffset="9573"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/core/util/DistanceCalculator.java"
            line="262"
            column="20"
            startOffset="10093"
            endLine="262"
            endColumn="65"
            endOffset="10138"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/core/util/DistanceCalculator.java"
            line="264"
            column="20"
            startOffset="10176"
            endLine="264"
            endColumn="52"
            endOffset="10208"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/core/util/DistanceCalculator.java"
            line="274"
            column="20"
            startOffset="10434"
            endLine="274"
            endColumn="47"
            endOffset="10461"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/core/util/DistanceCalculator.java"
            line="276"
            column="20"
            startOffset="10499"
            endLine="276"
            endColumn="60"
            endOffset="10539"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/service/DummyPlaceSearchService.java"
            line="302"
            column="20"
            startOffset="12373"
            endLine="302"
            endColumn="60"
            endOffset="12413"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/EnhancedDirectionsService.java"
            line="140"
            column="39"
            startOffset="5570"
            endLine="140"
            endColumn="83"
            endOffset="5614"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="306"
            column="37"
            startOffset="10900"
            endLine="306"
            endColumn="85"
            endOffset="10948"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="315"
            column="35"
            startOffset="11261"
            endLine="315"
            endColumn="84"
            endOffset="11310"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="318"
            column="34"
            startOffset="11392"
            endLine="318"
            endColumn="81"
            endOffset="11439"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/recommendation/ImageCrawlingService.java"
            line="146"
            column="31"
            startOffset="4888"
            endLine="146"
            endColumn="42"
            endOffset="4899"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/recommendation/ImageCrawlingService.java"
            line="158"
            column="31"
            startOffset="5309"
            endLine="158"
            endColumn="42"
            endOffset="5320"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/LocalPlaceSearchService.java"
            line="130"
            column="39"
            startOffset="4102"
            endLine="130"
            endColumn="50"
            endOffset="4113"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/LocalPlaceSearchService.java"
            line="133"
            column="28"
            startOffset="4189"
            endLine="133"
            endColumn="39"
            endOffset="4200"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/LocalPlaceSearchService.java"
            line="134"
            column="31"
            startOffset="4259"
            endLine="134"
            endColumn="42"
            endOffset="4270"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/LocalPlaceSearchService.java"
            line="135"
            column="32"
            startOffset="4330"
            endLine="135"
            endColumn="43"
            endOffset="4341"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="260"
            column="21"
            startOffset="10593"
            endLine="260"
            endColumn="55"
            endOffset="10627"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="355"
            column="34"
            startOffset="14327"
            endLine="355"
            endColumn="68"
            endOffset="14361"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="396"
            column="34"
            startOffset="15759"
            endLine="396"
            endColumn="68"
            endOffset="15793"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="453"
            column="30"
            startOffset="17737"
            endLine="453"
            endColumn="64"
            endOffset="17771"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="495"
            column="33"
            startOffset="19139"
            endLine="495"
            endColumn="67"
            endOffset="19173"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="545"
            column="31"
            startOffset="20858"
            endLine="545"
            endColumn="82"
            endOffset="20909"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="553"
            column="27"
            startOffset="21264"
            endLine="553"
            endColumn="59"
            endOffset="21296"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="590"
            column="34"
            startOffset="22583"
            endLine="590"
            endColumn="68"
            endOffset="22617"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="592"
            column="30"
            startOffset="22717"
            endLine="592"
            endColumn="66"
            endOffset="22753"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="631"
            column="32"
            startOffset="24136"
            endLine="631"
            endColumn="66"
            endOffset="24170"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="676"
            column="33"
            startOffset="25749"
            endLine="676"
            endColumn="67"
            endOffset="25783"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/MultiModalRouteService.java"
            line="1042"
            column="31"
            startOffset="39633"
            endLine="1042"
            endColumn="82"
            endOffset="39684"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverDirectionsService.java"
            line="238"
            column="35"
            startOffset="9224"
            endLine="238"
            endColumn="86"
            endOffset="9275"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverDirectionsService.java"
            line="242"
            column="35"
            startOffset="9410"
            endLine="242"
            endColumn="83"
            endOffset="9458"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/NaverLocalSearchService.java"
            line="500"
            column="26"
            startOffset="23447"
            endLine="500"
            endColumn="37"
            endOffset="23458"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/NaverLocalSearchService.java"
            line="530"
            column="26"
            startOffset="24247"
            endLine="530"
            endColumn="37"
            endOffset="24258"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverOptimalRouteService.java"
            line="81"
            column="24"
            startOffset="2536"
            endLine="81"
            endColumn="66"
            endOffset="2578"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverOptimalRouteService.java"
            line="89"
            column="24"
            startOffset="2750"
            endLine="89"
            endColumn="51"
            endOffset="2777"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverPlaceKeywordService.java"
            line="370"
            column="39"
            startOffset="14272"
            endLine="370"
            endColumn="50"
            endOffset="14283"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverPlaceSearchService.java"
            line="84"
            column="20"
            startOffset="2611"
            endLine="84"
            endColumn="76"
            endOffset="2667"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverPlaceSearchService.java"
            line="519"
            column="38"
            startOffset="19928"
            endLine="519"
            endColumn="49"
            endOffset="19939"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverPlaceSearchService.java"
            line="520"
            column="39"
            startOffset="19981"
            endLine="520"
            endColumn="50"
            endOffset="19992"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverPlaceSearchService.java"
            line="573"
            column="38"
            startOffset="22622"
            endLine="573"
            endColumn="49"
            endOffset="22633"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverPlaceSearchService.java"
            line="574"
            column="39"
            startOffset="22675"
            endLine="574"
            endColumn="50"
            endOffset="22686"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverTransitService.java"
            line="269"
            column="20"
            startOffset="9570"
            endLine="269"
            endColumn="57"
            endOffset="9607"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverTransitService.java"
            line="271"
            column="20"
            startOffset="9645"
            endLine="271"
            endColumn="63"
            endOffset="9688"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/NaverTransitService.java"
            line="279"
            column="16"
            startOffset="9800"
            endLine="279"
            endColumn="44"
            endOffset="9828"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/OOTDAdapter.java"
            line="128"
            column="31"
            startOffset="4436"
            endLine="128"
            endColumn="42"
            endOffset="4447"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/model/OOTDRecommendation.java"
            line="41"
            column="39"
            startOffset="1515"
            endLine="41"
            endColumn="50"
            endOffset="1526"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/model/OOTDRecommendation.java"
            line="41"
            column="77"
            startOffset="1553"
            endLine="41"
            endColumn="88"
            endOffset="1564"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/model/OOTDRecommendation.java"
            line="43"
            column="37"
            startOffset="1674"
            endLine="43"
            endColumn="48"
            endOffset="1685"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/model/OOTDRecommendation.java"
            line="43"
            column="74"
            startOffset="1711"
            endLine="43"
            endColumn="85"
            endOffset="1722"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/adapters/OOTDRecommendationAdapter.java"
            line="124"
            column="30"
            startOffset="4395"
            endLine="124"
            endColumn="41"
            endOffset="4406"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/adapters/OOTDRecommendationAdapter.java"
            line="145"
            column="30"
            startOffset="5209"
            endLine="145"
            endColumn="41"
            endOffset="5220"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/ootd/OOTDRecommendationService.java"
            line="182"
            column="39"
            startOffset="5804"
            endLine="182"
            endColumn="50"
            endOffset="5815"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/service/OOTDRecommendationService.java"
            line="518"
            column="37"
            startOffset="17829"
            endLine="518"
            endColumn="48"
            endOffset="17840"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/profile/ProfileActivity.java"
            line="216"
            column="51"
            startOffset="7641"
            endLine="216"
            endColumn="125"
            endOffset="7715"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/profile/ProfileActivity.java"
            line="219"
            column="49"
            startOffset="7839"
            endLine="219"
            endColumn="85"
            endOffset="7875"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/RealTimeTrafficService.java"
            line="159"
            column="55"
            startOffset="5748"
            endLine="159"
            endColumn="66"
            endOffset="5759"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
            line="1089"
            column="20"
            startOffset="43465"
            endLine="1089"
            endColumn="47"
            endOffset="43492"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationAdapter.java"
            line="107"
            column="37"
            startOffset="3957"
            endLine="107"
            endColumn="74"
            endOffset="3994"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/notification/ReminderNotificationHelper.java"
            line="69"
            column="30"
            startOffset="2319"
            endLine="71"
            endColumn="103"
            endOffset="2545"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/RetrofitNaverDirectionsService.java"
            line="263"
            column="20"
            startOffset="9483"
            endLine="263"
            endColumn="47"
            endOffset="9510"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/RetrofitNaverDirectionsService.java"
            line="290"
            column="20"
            startOffset="10153"
            endLine="290"
            endColumn="48"
            endOffset="10181"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="915"
            column="17"
            startOffset="33661"
            endLine="915"
            endColumn="50"
            endOffset="33694"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="927"
            column="17"
            startOffset="34192"
            endLine="927"
            endColumn="50"
            endOffset="34225"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="939"
            column="17"
            startOffset="34713"
            endLine="939"
            endColumn="50"
            endOffset="34746"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="951"
            column="17"
            startOffset="35210"
            endLine="951"
            endColumn="50"
            endOffset="35243"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="963"
            column="17"
            startOffset="35698"
            endLine="963"
            endColumn="50"
            endOffset="35731"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1037"
            column="24"
            startOffset="38278"
            endLine="1037"
            endColumn="76"
            endOffset="38330"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1039"
            column="24"
            startOffset="38376"
            endLine="1039"
            endColumn="55"
            endOffset="38407"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1054"
            column="28"
            startOffset="38786"
            endLine="1054"
            endColumn="62"
            endOffset="38820"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1057"
            column="28"
            startOffset="38969"
            endLine="1057"
            endColumn="59"
            endOffset="39000"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1060"
            column="28"
            startOffset="39153"
            endLine="1060"
            endColumn="62"
            endOffset="39187"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1270"
            column="34"
            startOffset="46404"
            endLine="1273"
            endColumn="60"
            endOffset="46604"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1301"
            column="28"
            startOffset="47742"
            endLine="1301"
            endColumn="77"
            endOffset="47791"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1483"
            column="28"
            startOffset="55515"
            endLine="1483"
            endColumn="66"
            endOffset="55553"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1527"
            column="38"
            startOffset="57375"
            endLine="1527"
            endColumn="103"
            endOffset="57440"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1662"
            column="41"
            startOffset="63301"
            endLine="1662"
            endColumn="106"
            endOffset="63366"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1737"
            column="60"
            startOffset="66558"
            endLine="1737"
            endColumn="91"
            endOffset="66589"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1761"
            column="32"
            startOffset="67668"
            endLine="1761"
            endColumn="65"
            endOffset="67701"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1795"
            column="64"
            startOffset="69592"
            endLine="1795"
            endColumn="95"
            endOffset="69623"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1858"
            column="54"
            startOffset="72381"
            endLine="1858"
            endColumn="86"
            endOffset="72413"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1889"
            column="30"
            startOffset="74142"
            endLine="1889"
            endColumn="91"
            endOffset="74203"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2126"
            column="38"
            startOffset="84086"
            endLine="2126"
            endColumn="49"
            endOffset="84097"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2127"
            column="39"
            startOffset="84139"
            endLine="2127"
            endColumn="50"
            endOffset="84150"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2261"
            column="31"
            startOffset="90756"
            endLine="2261"
            endColumn="62"
            endOffset="90787"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2268"
            column="30"
            startOffset="91023"
            endLine="2268"
            endColumn="61"
            endOffset="91054"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2275"
            column="29"
            startOffset="91288"
            endLine="2275"
            endColumn="60"
            endOffset="91319"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2282"
            column="29"
            startOffset="91647"
            endLine="2282"
            endColumn="60"
            endOffset="91678"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2409"
            column="36"
            startOffset="97537"
            endLine="2409"
            endColumn="72"
            endOffset="97573"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2776"
            column="44"
            startOffset="111916"
            endLine="2776"
            endColumn="55"
            endOffset="111927"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2848"
            column="48"
            startOffset="114665"
            endLine="2848"
            endColumn="59"
            endOffset="114676"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleCalendarActivity.java"
            line="417"
            column="22"
            startOffset="15803"
            endLine="417"
            endColumn="33"
            endOffset="15814"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleListActivity.java"
            line="893"
            column="22"
            startOffset="34364"
            endLine="893"
            endColumn="33"
            endOffset="34375"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleListActivity.java"
            line="907"
            column="22"
            startOffset="34763"
            endLine="907"
            endColumn="33"
            endOffset="34774"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminder.java"
            line="95"
            column="24"
            startOffset="3020"
            endLine="95"
            endColumn="82"
            endOffset="3078"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminder.java"
            line="132"
            column="16"
            startOffset="3860"
            endLine="133"
            endColumn="80"
            endOffset="3980"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/worker/ScheduleWorker.java"
            line="304"
            column="39"
            startOffset="11894"
            endLine="304"
            endColumn="50"
            endOffset="11905"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/WeatherData.java"
            line="94"
            column="29"
            startOffset="2165"
            endLine="94"
            endColumn="40"
            endOffset="2176"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/WeatherService.java"
            line="42"
            column="20"
            startOffset="1177"
            endLine="42"
            endColumn="81"
            endOffset="1238"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/WeatherService.java"
            line="44"
            column="20"
            startOffset="1303"
            endLine="45"
            endColumn="63"
            endOffset="1415"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/WeatherService.java"
            line="46"
            column="20"
            startOffset="1312"
            endLine="47"
            endColumn="69"
            endOffset="1435"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/network/api/WeatherService.java"
            line="63"
            column="36"
            startOffset="1828"
            endLine="66"
            endColumn="18"
            endOffset="1993"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="63"
            column="57"
            startOffset="2082"
            endLine="63"
            endColumn="101"
            endOffset="2126"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="107"
            column="57"
            startOffset="3900"
            endLine="107"
            endColumn="101"
            endOffset="3944"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="148"
            column="57"
            startOffset="5547"
            endLine="148"
            endColumn="101"
            endOffset="5591"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/model/Schedule.java"
            line="84"
            column="49"
            startOffset="2308"
            endLine="84"
            endColumn="99"
            endOffset="2358"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/model/Schedule.java"
            line="93"
            column="53"
            startOffset="2656"
            endLine="93"
            endColumn="97"
            endOffset="2700"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/model/Schedule.java"
            line="94"
            column="53"
            startOffset="2754"
            endLine="94"
            endColumn="92"
            endOffset="2793"/>
    </incident>

    <incident
        id="SimpleDateFormat"
        severity="warning"
        message="To get local formatting use `getDateInstance()`, `getDateTimeInstance()`, or `getTimeInstance()`, or use `new SimpleDateFormat(String template, Locale locale)` with for example `Locale.US` for ASCII dates.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/data/repository/ScheduleRepository.java"
            line="154"
            column="57"
            startOffset="5073"
            endLine="154"
            endColumn="101"
            endOffset="5117"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1413"
            column="89"
            startOffset="51476"
            endLine="1413"
            endColumn="93"
            endOffset="51480"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleListActivity.java"
            line="971"
            column="84"
            startOffset="37254"
            endLine="971"
            endColumn="88"
            endOffset="37258"/>
    </incident>

    <incident
        id="UseSwitchCompatOrMaterialCode"
        severity="warning"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/profile/ProfileActivity.java"
            line="60"
            column="5"
            startOffset="1979"
            endLine="60"
            endColumn="39"
            endOffset="2013"/>
    </incident>

    <incident
        id="UseSwitchCompatOrMaterialXml"
        severity="warning"
        message="Use `SwitchCompat` from AppCompat or `MaterialSwitch` from Material library">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="303"
            column="13"
            startOffset="10722"
            endLine="309"
            endColumn="43"
            endOffset="11014"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.9.0 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.1"
                family="Update versions"
                oldString="8.9.0"
                replacement="8.10.1"
                priority="0"/>
            <fix-replace
                description="Change to 8.9.3"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.9.0"
                replacement="8.9.3"
                priority="0"/>
        </fix-alternatives>
        <location
            file="$HOME/TimeMate/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.1">
        <fix-replace
            description="Change to 2.7.1"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="67"
            column="25"
            startOffset="2519"
            endLine="67"
            endColumn="60"
            endOffset="2554"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.kakao.sdk:v2-user than 2.19.0 is available: 2.21.3">
        <fix-replace
            description="Change to 2.21.3"
            family="Update versions"
            oldString="2.19.0"
            replacement="2.21.3"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="68"
            column="21"
            startOffset="2576"
            endLine="68"
            endColumn="51"
            endOffset="2606"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="84"
            column="20"
            startOffset="3197"
            endLine="84"
            endColumn="54"
            endOffset="3231"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.work:work-runtime than 2.8.1 is available: 2.10.1">
        <fix-replace
            description="Change to 2.10.1"
            family="Update versions"
            oldString="2.8.1"
            replacement="2.10.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="87"
            column="20"
            startOffset="3293"
            endLine="87"
            endColumn="54"
            endOffset="3327"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="100"
            column="29"
            startOffset="3974"
            endLine="100"
            endColumn="52"
            endOffset="3997"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="200"
            column="29"
            startOffset="8635"
            endLine="200"
            endColumn="52"
            endOffset="8658"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="300"
            column="29"
            startOffset="13291"
            endLine="300"
            endColumn="52"
            endOffset="13314"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="400"
            column="29"
            startOffset="17941"
            endLine="400"
            endColumn="52"
            endOffset="17964"/>
    </incident>

    <incident
        id="SpUsage"
        severity="warning"
        message="Should use &quot;`sp`&quot; instead of &quot;`dp`&quot; for text sizes">
        <fix-replace
            description="Replace with sp"
            oldPattern=".*(di?p)"
            replacement="sp"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="500"
            column="29"
            startOffset="22590"
            endLine="500"
            endColumn="52"
            endOffset="22613"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_hint&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_hint"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_home_schedule.xml"
            line="66"
            column="9"
            startOffset="2319"
            endLine="66"
            endColumn="40"
            endOffset="2350"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Use app namespace instead of android">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_phone&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_phone"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="132"
            column="17"
            startOffset="5183"
            endLine="132"
            endColumn="59"
            endOffset="5225"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Use app namespace instead of android">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_person&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_person"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="160"
            column="17"
            startOffset="6023"
            endLine="160"
            endColumn="60"
            endOffset="6066"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Use app namespace instead of android">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_access_time&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_access_time"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="54"
            column="17"
            startOffset="1985"
            endLine="54"
            endColumn="65"
            endOffset="2033"/>
    </incident>

    <incident
        id="UseCompatTextViewDrawableXml"
        severity="warning"
        message="Use `app:drawableStartCompat` instead of `android:drawableStart`">
        <fix-composite
            description="Use app namespace instead of android">
            <fix-attribute
                description="Set drawableStartCompat=&quot;@drawable/ic_location_on&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="drawableStartCompat"
                value="@drawable/ic_location_on"/>
            <fix-attribute
                description="Delete drawableStart"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="drawableStart"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="67"
            column="17"
            startOffset="2542"
            endLine="67"
            endColumn="65"
            endOffset="2590"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/AccountAdapter.java"
            line="120"
            column="9"
            startOffset="4463"
            endLine="120"
            endColumn="31"
            endOffset="4485"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/AccountSwitchActivity.java"
            line="209"
            column="25"
            startOffset="7859"
            endLine="209"
            endColumn="55"
            endOffset="7889"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/friend/FriendListActivity.java"
            line="154"
            column="21"
            startOffset="5445"
            endLine="154"
            endColumn="57"
            endOffset="5481"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/friend/adapter/FriendListAdapter.java"
            line="60"
            column="9"
            startOffset="1730"
            endLine="60"
            endColumn="31"
            endOffset="1752"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/ImprovedScheduleAdapter.java"
            line="233"
            column="9"
            startOffset="9365"
            endLine="233"
            endColumn="31"
            endOffset="9387"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/adapters/NotificationAdapter.java"
            line="58"
            column="9"
            startOffset="1819"
            endLine="58"
            endColumn="31"
            endOffset="1841"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/OOTDAdapter.java"
            line="62"
            column="9"
            startOffset="1871"
            endLine="62"
            endColumn="31"
            endOffset="1893"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/adapters/OOTDRecommendationAdapter.java"
            line="55"
            column="9"
            startOffset="1736"
            endLine="55"
            endColumn="31"
            endOffset="1758"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/PlaceSuggestAdapter.java"
            line="90"
            column="9"
            startOffset="2506"
            endLine="90"
            endColumn="31"
            endOffset="2528"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/PlaceSuggestAdapter.java"
            line="95"
            column="9"
            startOffset="2609"
            endLine="95"
            endColumn="31"
            endOffset="2631"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/recommendation/PlaceWithImageAdapter.java"
            line="51"
            column="9"
            startOffset="1747"
            endLine="51"
            endColumn="31"
            endOffset="1769"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
            line="1887"
            column="25"
            startOffset="74419"
            endLine="1887"
            endColumn="60"
            endOffset="74454"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
            line="1895"
            column="29"
            startOffset="74874"
            endLine="1895"
            endColumn="64"
            endOffset="74909"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationAdapter.java"
            line="67"
            column="9"
            startOffset="2006"
            endLine="67"
            endColumn="31"
            endOffset="2028"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/RouteOptionAdapter.java"
            line="46"
            column="9"
            startOffset="1369"
            endLine="46"
            endColumn="31"
            endOffset="1391"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleCalendarActivity.java"
            line="232"
            column="9"
            startOffset="8316"
            endLine="232"
            endColumn="47"
            endOffset="8354"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleCalendarActivity.java"
            line="503"
            column="25"
            startOffset="18757"
            endLine="503"
            endColumn="63"
            endOffset="18795"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/ScheduleDetailAdapter.java"
            line="139"
            column="9"
            startOffset="4973"
            endLine="139"
            endColumn="31"
            endOffset="4995"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleListActivity.java"
            line="704"
            column="25"
            startOffset="26471"
            endLine="704"
            endColumn="63"
            endOffset="26509"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleListActivity.java"
            line="1213"
            column="25"
            startOffset="47001"
            endLine="1213"
            endColumn="63"
            endOffset="47039"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/ScheduleListAdapter.java"
            line="63"
            column="9"
            startOffset="1967"
            endLine="63"
            endColumn="31"
            endOffset="1989"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/TodayScheduleAdapter.java"
            line="62"
            column="9"
            startOffset="1838"
            endLine="62"
            endColumn="31"
            endOffset="1860"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/TomorrowReminderAdapter.java"
            line="83"
            column="9"
            startOffset="2667"
            endLine="83"
            endColumn="31"
            endOffset="2689"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 27">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/NotificationService.java"
            line="31"
            column="13"
            startOffset="1022"
            endLine="31"
            endColumn="59"
            endOffset="1068"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 27">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/notification/ReminderNotificationHelper.java"
            line="40"
            column="13"
            startOffset="1222"
            endLine="40"
            endColumn="59"
            endOffset="1268"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 27">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="662"
            column="21"
            startOffset="23197"
            endLine="662"
            endColumn="89"
            endOffset="23265"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="314"
            column="22"
            startOffset="13771"
            endLine="314"
            endColumn="34"
            endOffset="13783"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="395"
            column="18"
            startOffset="17517"
            endLine="395"
            endColumn="30"
            endOffset="17529"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="59"
            column="14"
            startOffset="1984"
            endLine="59"
            endColumn="26"
            endOffset="1996"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="19"
            column="6"
            startOffset="742"
            endLine="19"
            endColumn="18"
            endOffset="754"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="65"
            column="14"
            startOffset="2421"
            endLine="65"
            endColumn="26"
            endOffset="2433"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="21"
            column="6"
            startOffset="692"
            endLine="21"
            endColumn="18"
            endOffset="704"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="54"
            column="10"
            startOffset="1757"
            endLine="54"
            endColumn="22"
            endOffset="1769"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="78"
            column="10"
            startOffset="2617"
            endLine="78"
            endColumn="22"
            endOffset="2629"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="104"
            column="6"
            startOffset="3465"
            endLine="104"
            endColumn="18"
            endOffset="3477"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="19"
            column="10"
            startOffset="659"
            endLine="19"
            endColumn="22"
            endOffset="671"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="57"
            column="10"
            startOffset="1987"
            endLine="57"
            endColumn="22"
            endOffset="1999"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (944 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_weather.xml"
            line="7"
            column="27"
            startOffset="257"
            endLine="7"
            endColumn="971"
            endOffset="1201"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="57"
            column="18"
            startOffset="2288"
            endLine="57"
            endColumn="30"
            endOffset="2300"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="207"
            column="14"
            startOffset="9043"
            endLine="207"
            endColumn="26"
            endOffset="9055"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="16"
            column="6"
            startOffset="636"
            endLine="16"
            endColumn="18"
            endOffset="648"/>
    </incident>

    <incident
        id="DisableBaselineAlignment"
        severity="warning"
        message="Set `android:baselineAligned=&quot;false&quot;` on this element for better performance">
        <fix-attribute
            description="Set baselineAligned=&quot;false&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="baselineAligned"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule.xml"
            line="16"
            column="6"
            startOffset="684"
            endLine="16"
            endColumn="18"
            endOffset="696"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="101"
            column="13"
            startOffset="3620"
            endLine="101"
            endColumn="38"
            endOffset="3645"/>
    </incident>

    <incident
        id="NestedWeights"
        severity="warning"
        message="Nested weights are bad for performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_ootd_recommendation.xml"
            line="74"
            column="17"
            startOffset="2767"
            endLine="74"
            endColumn="42"
            endOffset="2792"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_secondary` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="7"
            column="5"
            startOffset="288"
            endLine="7"
            endColumn="53"
            endOffset="336"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="7"
            column="5"
            startOffset="294"
            endLine="7"
            endColumn="51"
            endOffset="340"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml"
            line="6"
            column="5"
            startOffset="295"
            endLine="6"
            endColumn="33"
            endOffset="323"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_secondary` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="7"
            column="5"
            startOffset="294"
            endLine="7"
            endColumn="53"
            endOffset="342"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="7"
            column="5"
            startOffset="294"
            endLine="7"
            endColumn="33"
            endOffset="322"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_light` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="6"
            column="5"
            startOffset="297"
            endLine="6"
            endColumn="49"
            endOffset="341"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="5"
            column="5"
            startOffset="195"
            endLine="5"
            endColumn="43"
            endOffset="233"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#f5f5f5` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="7"
            column="5"
            startOffset="294"
            endLine="7"
            endColumn="33"
            endOffset="322"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="6"
            column="5"
            startOffset="297"
            endLine="6"
            endColumn="43"
            endOffset="335"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_secondary` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="7"
            column="5"
            startOffset="294"
            endLine="7"
            endColumn="53"
            endOffset="342"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_light` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_calendar.xml"
            line="7"
            column="5"
            startOffset="343"
            endLine="7"
            endColumn="49"
            endOffset="387"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="6"
            column="5"
            startOffset="297"
            endLine="6"
            endColumn="43"
            endOffset="335"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_light` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="7"
            column="5"
            startOffset="288"
            endLine="7"
            endColumn="49"
            endOffset="332"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_autocomplete.xml"
            line="7"
            column="5"
            startOffset="259"
            endLine="7"
            endColumn="64"
            endOffset="318"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggest.xml"
            line="8"
            column="5"
            startOffset="315"
            endLine="8"
            endColumn="64"
            endOffset="374"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `?android:attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggestion.xml"
            line="8"
            column="5"
            startOffset="317"
            endLine="8"
            endColumn="64"
            endOffset="376"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#33B5DFFF` with a theme that also paints a background (inferred theme is `@style/TimeMateTheme`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="9"
            column="5"
            startOffset="366"
            endLine="9"
            endColumn="35"
            endOffset="396"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="76"
            column="26"
            startOffset="3187"
            endLine="76"
            endColumn="38"
            endOffset="3199"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
                    startOffset="5796"
                    endOffset="6743"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
                    startOffset="6438"
                    endOffset="6718"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="165"
            column="14"
            startOffset="6439"
            endLine="165"
            endColumn="20"
            endOffset="6445"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
                    startOffset="2214"
                    endOffset="3227"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
                    startOffset="2917"
                    endOffset="3200"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="83"
            column="14"
            startOffset="2918"
            endLine="83"
            endColumn="20"
            endOffset="2924"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
                    startOffset="3323"
                    endOffset="4618"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
                    startOffset="3626"
                    endOffset="4125"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
                    startOffset="4135"
                    endOffset="4597"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="99"
            column="10"
            startOffset="3627"
            endLine="99"
            endColumn="16"
            endOffset="3633"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
                    startOffset="3323"
                    endOffset="4618"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
                    startOffset="3626"
                    endOffset="4125"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
                    startOffset="4135"
                    endOffset="4597"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="111"
            column="10"
            startOffset="4136"
            endLine="111"
            endColumn="16"
            endOffset="4142"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
                    startOffset="1958"
                    endOffset="2774"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
                    startOffset="2445"
                    endOffset="2753"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
            line="74"
            column="10"
            startOffset="2446"
            endLine="74"
            endColumn="16"
            endOffset="2452"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
                    startOffset="5371"
                    endOffset="6228"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
                    startOffset="5896"
                    endOffset="6207"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="177"
            column="10"
            startOffset="5897"
            endLine="177"
            endColumn="16"
            endOffset="5903"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
                    startOffset="2827"
                    endOffset="3980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
                    startOffset="3031"
                    endOffset="3516"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
                    startOffset="3530"
                    endOffset="3955"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="87"
            column="14"
            startOffset="3032"
            endLine="87"
            endColumn="20"
            endOffset="3038"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
                    startOffset="2827"
                    endOffset="3980"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
                    startOffset="3031"
                    endOffset="3516"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
                    startOffset="3530"
                    endOffset="3955"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="99"
            column="14"
            startOffset="3531"
            endLine="99"
            endColumn="20"
            endOffset="3537"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="7"
            column="6"
            startOffset="229"
            endLine="7"
            endColumn="14"
            endOffset="237"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="72"
            column="18"
            startOffset="2619"
            endLine="72"
            endColumn="26"
            endOffset="2627"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="12"
            column="10"
            startOffset="389"
            endLine="12"
            endColumn="18"
            endOffset="397"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="51"
            column="10"
            startOffset="1745"
            endLine="51"
            endColumn="18"
            endOffset="1753"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend.xml"
            line="99"
            column="21"
            startOffset="3942"
            endLine="99"
            endColumn="44"
            endOffset="3965"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend.xml"
            line="110"
            column="21"
            startOffset="4480"
            endLine="110"
            endColumn="44"
            endOffset="4503"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="48"
            column="17"
            startOffset="1870"
            endLine="48"
            endColumn="40"
            endOffset="1893"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="117"
            column="22"
            startOffset="4569"
            endLine="117"
            endColumn="30"
            endOffset="4577"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="167"
            column="22"
            startOffset="6995"
            endLine="167"
            endColumn="30"
            endOffset="7003"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="7"
            column="6"
            startOffset="229"
            endLine="7"
            endColumn="14"
            endOffset="237"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="13"
            column="6"
            startOffset="406"
            endLine="13"
            endColumn="14"
            endOffset="414"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="72"
            column="18"
            startOffset="2619"
            endLine="72"
            endColumn="26"
            endOffset="2627"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="332"
            column="18"
            startOffset="14802"
            endLine="332"
            endColumn="26"
            endOffset="14810"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="12"
            column="10"
            startOffset="389"
            endLine="12"
            endColumn="18"
            endOffset="397"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="18"
            column="10"
            startOffset="587"
            endLine="18"
            endColumn="18"
            endOffset="595"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="25"
            column="10"
            startOffset="833"
            endLine="25"
            endColumn="18"
            endOffset="841"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="51"
            column="10"
            startOffset="1745"
            endLine="51"
            endColumn="18"
            endOffset="1753"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="57"
            column="10"
            startOffset="1945"
            endLine="57"
            endColumn="18"
            endOffset="1953"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-room-compiler"
            robot="true">
            <fix-replace
                description="Replace with roomCompilerVersion = &quot;2.6.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="roomCompilerVersion = &quot;2.6.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="199"
                    endOffset="199"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-room-compiler = { module = &quot;androidx.room:room-compiler&quot;, version.ref = &quot;roomCompilerVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-room-compiler = { module = &quot;androidx.room:room-compiler&quot;, version.ref = &quot;roomCompilerVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="241"
                    endOffset="241"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.room.compiler"
                robot="true"
                replacement="libs.androidx.room.compiler"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2519"
                    endOffset="2554"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="67"
            column="25"
            startOffset="2519"
            endLine="67"
            endColumn="60"
            endOffset="2554"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for sdk-v2-user"
            robot="true">
            <fix-replace
                description="Replace with v2UserVersion = &quot;2.19.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="v2UserVersion = &quot;2.19.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with sdk-v2-user = { module = &quot;com.kakao.sdk:v2-user&quot;, version.ref = &quot;v2UserVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="sdk-v2-user = { module = &quot;com.kakao.sdk:v2-user&quot;, version.ref = &quot;v2UserVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="1124"
                    endOffset="1124"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.sdk.v2.user"
                robot="true"
                replacement="libs.sdk.v2.user"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2576"
                    endOffset="2606"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="68"
            column="21"
            startOffset="2576"
            endLine="68"
            endColumn="51"
            endOffset="2606"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp3-okhttp"
            robot="true">
            <fix-replace
                description="Replace with okhttpVersion = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttpVersion = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="175"
                    endOffset="175"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp3-okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;okhttpVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp3-okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;okhttpVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="903"
                    endOffset="903"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp3.okhttp"
                robot="true"
                replacement="libs.okhttp3.okhttp"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2653"
                    endOffset="2689"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="71"
            column="20"
            startOffset="2653"
            endLine="71"
            endColumn="56"
            endOffset="2689"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-retrofit"
            robot="true">
            <fix-replace
                description="Replace with retrofitVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofitVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="175"
                    endOffset="175"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-retrofit = { module = &quot;com.squareup.retrofit2:retrofit&quot;, version.ref = &quot;retrofitVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="903"
                    endOffset="903"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.retrofit"
                robot="true"
                replacement="libs.retrofit2.retrofit"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2710"
                    endOffset="2749"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="72"
            column="20"
            startOffset="2710"
            endLine="72"
            endColumn="59"
            endOffset="2749"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for retrofit2-converter-gson"
            robot="true">
            <fix-replace
                description="Replace with converterGsonVersion = &quot;2.9.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="converterGsonVersion = &quot;2.9.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="retrofit2-converter-gson = { module = &quot;com.squareup.retrofit2:converter-gson&quot;, version.ref = &quot;converterGsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="903"
                    endOffset="903"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.retrofit2.converter.gson"
                robot="true"
                replacement="libs.retrofit2.converter.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2770"
                    endOffset="2815"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="73"
            column="20"
            startOffset="2770"
            endLine="73"
            endColumn="65"
            endOffset="2815"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp3-logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with loggingInterceptorVersion = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="loggingInterceptorVersion = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="108"
                    endOffset="108"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp3-logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptorVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="903"
                    endOffset="903"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp3.logging.interceptor"
                robot="true"
                replacement="libs.okhttp3.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2836"
                    endOffset="2885"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="74"
            column="20"
            startOffset="2836"
            endLine="74"
            endColumn="69"
            endOffset="2885"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for github-glide"
            robot="true">
            <fix-replace
                description="Replace with glideVersion = &quot;4.16.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="glideVersion = &quot;4.16.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with github-glide = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;glideVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="github-glide = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;glideVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="241"
                    endOffset="241"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.github.glide"
                robot="true"
                replacement="libs.github.glide"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="2935"
                    endOffset="2975"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="77"
            column="20"
            startOffset="2935"
            endLine="77"
            endColumn="60"
            endOffset="2975"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for glide-compiler"
            robot="true">
            <fix-replace
                description="Replace with compilerVersion = &quot;4.16.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="compilerVersion = &quot;4.16.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with glide-compiler = { module = &quot;com.github.bumptech.glide:compiler&quot;, version.ref = &quot;compilerVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="glide-compiler = { module = &quot;com.github.bumptech.glide:compiler&quot;, version.ref = &quot;compilerVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="241"
                    endOffset="241"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.glide.compiler"
                robot="true"
                replacement="libs.glide.compiler"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3001"
                    endOffset="3044"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="78"
            column="25"
            startOffset="3001"
            endLine="78"
            endColumn="68"
            endOffset="3044"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for jsoup-jsoup"
            robot="true">
            <fix-replace
                description="Replace with jsoupVersion = &quot;1.16.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jsoupVersion = &quot;1.16.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with jsoup-jsoup = { module = &quot;org.jsoup:jsoup&quot;, version.ref = &quot;jsoupVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="jsoup-jsoup = { module = &quot;org.jsoup:jsoup&quot;, version.ref = &quot;jsoupVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="241"
                    endOffset="241"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.jsoup.jsoup"
                robot="true"
                replacement="libs.jsoup.jsoup"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3106"
                    endOffset="3130"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="81"
            column="20"
            startOffset="3106"
            endLine="81"
            endColumn="44"
            endOffset="3130"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for google-gson"
            robot="true">
            <fix-replace
                description="Replace with gsonVersion = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gsonVersion = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="25"
                    endOffset="25"/>
            </fix-replace>
            <fix-replace
                description="Replace with google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="google-gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gsonVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="241"
                    endOffset="241"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.google.gson"
                robot="true"
                replacement="libs.google.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3197"
                    endOffset="3231"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="84"
            column="20"
            startOffset="3197"
            endLine="84"
            endColumn="54"
            endOffset="3231"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for androidx-work-runtime"
            robot="true">
            <fix-replace
                description="Replace with workRuntimeVersion = &quot;2.8.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="workRuntimeVersion = &quot;2.8.1&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with androidx-work-runtime = { module = &quot;androidx.work:work-runtime&quot;, version.ref = &quot;workRuntimeVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="androidx-work-runtime = { module = &quot;androidx.work:work-runtime&quot;, version.ref = &quot;workRuntimeVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/TimeMate/gradle/libs.versions.toml"
                    startOffset="241"
                    endOffset="241"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.androidx.work.runtime"
                robot="true"
                replacement="libs.androidx.work.runtime"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle.kts"
                    startOffset="3293"
                    endOffset="3327"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle.kts"
            line="87"
            column="20"
            startOffset="3293"
            endLine="87"
            endColumn="54"
            endOffset="3327"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view `CalendarView` overrides `onTouchEvent` but not `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>"
            line="190"
            column="20"
            startOffset="6678"
            endLine="190"
            endColumn="32"
            endOffset="6690"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` lambda should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="582"
            column="41"
            startOffset="20624"
            endLine="593"
            endColumn="14"
            endOffset="21118"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="19"
            column="10"
            startOffset="690"
            endLine="19"
            endColumn="21"
            endOffset="701"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="65"
            column="18"
            startOffset="2240"
            endLine="65"
            endColumn="27"
            endOffset="2249"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="46"
            column="22"
            startOffset="1808"
            endLine="46"
            endColumn="33"
            endOffset="1819"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="229"
            column="22"
            startOffset="10057"
            endLine="229"
            endColumn="31"
            endOffset="10066"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="260"
            column="22"
            startOffset="11443"
            endLine="260"
            endColumn="31"
            endOffset="11452"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="321"
            column="26"
            startOffset="14113"
            endLine="321"
            endColumn="35"
            endOffset="14122"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="402"
            column="22"
            startOffset="17835"
            endLine="402"
            endColumn="31"
            endOffset="17844"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="18"
            column="10"
            startOffset="631"
            endLine="18"
            endColumn="21"
            endOffset="642"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="22"
            column="14"
            startOffset="837"
            endLine="22"
            endColumn="25"
            endOffset="848"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="66"
            column="10"
            startOffset="2247"
            endLine="66"
            endColumn="19"
            endOffset="2256"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="18"
            column="10"
            startOffset="635"
            endLine="18"
            endColumn="21"
            endOffset="646"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
            line="28"
            column="10"
            startOffset="986"
            endLine="28"
            endColumn="21"
            endOffset="997"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="25"
            column="14"
            startOffset="905"
            endLine="25"
            endColumn="23"
            endOffset="914"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="44"
            column="14"
            startOffset="1655"
            endLine="44"
            endColumn="25"
            endOffset="1666"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="72"
            column="18"
            startOffset="2708"
            endLine="72"
            endColumn="27"
            endOffset="2717"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="97"
            column="18"
            startOffset="3686"
            endLine="97"
            endColumn="27"
            endOffset="3695"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="132"
            column="18"
            startOffset="5081"
            endLine="132"
            endColumn="27"
            endOffset="5090"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="168"
            column="18"
            startOffset="6497"
            endLine="168"
            endColumn="27"
            endOffset="6506"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="59"
            column="14"
            startOffset="2306"
            endLine="59"
            endColumn="25"
            endOffset="2317"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_account.xml"
            line="27"
            column="14"
            startOffset="946"
            endLine="27"
            endColumn="23"
            endOffset="955"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_account.xml"
            line="82"
            column="10"
            startOffset="2949"
            endLine="82"
            endColumn="19"
            endOffset="2958"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend.xml"
            line="23"
            column="10"
            startOffset="935"
            endLine="23"
            endColumn="19"
            endOffset="944"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_home_schedule.xml"
            line="62"
            column="6"
            startOffset="2177"
            endLine="62"
            endColumn="15"
            endOffset="2186"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="26"
            column="14"
            startOffset="942"
            endLine="26"
            endColumn="23"
            endOffset="951"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_ootd_recommendation.xml"
            line="22"
            column="14"
            startOffset="745"
            endLine="22"
            endColumn="23"
            endOffset="754"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggest.xml"
            line="19"
            column="10"
            startOffset="660"
            endLine="19"
            endColumn="19"
            endOffset="669"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggestion.xml"
            line="12"
            column="6"
            startOffset="442"
            endLine="12"
            endColumn="15"
            endOffset="451"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggestion.xml"
            line="65"
            column="6"
            startOffset="2135"
            endLine="65"
            endColumn="15"
            endOffset="2144"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="30"
            column="14"
            startOffset="1076"
            endLine="30"
            endColumn="23"
            endOffset="1085"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="163"
            column="14"
            startOffset="6656"
            endLine="163"
            endColumn="23"
            endOffset="6665"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="177"
            column="14"
            startOffset="7277"
            endLine="177"
            endColumn="23"
            endOffset="7286"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="22"
            column="10"
            startOffset="760"
            endLine="22"
            endColumn="19"
            endOffset="769"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="28"
            column="10"
            startOffset="931"
            endLine="28"
            endColumn="19"
            endOffset="940"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="61"
            column="14"
            startOffset="2019"
            endLine="61"
            endColumn="23"
            endOffset="2028"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="84"
            column="14"
            startOffset="2833"
            endLine="84"
            endColumn="23"
            endOffset="2842"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="113"
            column="10"
            startOffset="3778"
            endLine="113"
            endColumn="19"
            endOffset="3787"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="36"
            column="18"
            startOffset="1348"
            endLine="36"
            endColumn="27"
            endOffset="1357"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="72"
            column="14"
            startOffset="2732"
            endLine="72"
            endColumn="23"
            endOffset="2741"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="90"
            column="14"
            startOffset="3361"
            endLine="90"
            endColumn="23"
            endOffset="3370"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="106"
            column="14"
            startOffset="3981"
            endLine="106"
            endColumn="23"
            endOffset="3990"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="113"
            column="14"
            startOffset="4259"
            endLine="113"
            endColumn="23"
            endOffset="4268"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="25"
            column="14"
            startOffset="875"
            endLine="25"
            endColumn="23"
            endOffset="884"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="64"
            column="14"
            startOffset="2246"
            endLine="64"
            endColumn="23"
            endOffset="2255"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/AccountAdapter.java"
            line="68"
            column="32"
            startOffset="2330"
            endLine="68"
            endColumn="52"
            endOffset="2350"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/AccountAdapter.java"
            line="68"
            column="32"
            startOffset="2330"
            endLine="68"
            endColumn="38"
            endOffset="2336"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/AccountAdapter.java"
            line="83"
            column="37"
            startOffset="2917"
            endLine="83"
            endColumn="58"
            endOffset="2938"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/AccountSwitchActivity.java"
            line="127"
            column="45"
            startOffset="4580"
            endLine="127"
            endColumn="69"
            endOffset="4604"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/dialog/DirectionsBottomSheetDialog.java"
            line="145"
            column="33"
            startOffset="5159"
            endLine="145"
            endColumn="64"
            endOffset="5190"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/dialog/DirectionsBottomSheetDialog.java"
            line="146"
            column="32"
            startOffset="5224"
            endLine="146"
            endColumn="54"
            endOffset="5246"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/friend/adapter/FriendListAdapter.java"
            line="93"
            column="36"
            startOffset="2972"
            endLine="93"
            endColumn="61"
            endOffset="2997"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/adapters/FriendSelectionAdapter.java"
            line="75"
            column="34"
            startOffset="2795"
            endLine="75"
            endColumn="59"
            endOffset="2820"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/ImprovedScheduleAdapter.java"
            line="174"
            column="42"
            startOffset="6686"
            endLine="174"
            endColumn="88"
            endOffset="6732"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/adapters/OOTDRecommendationAdapter.java"
            line="96"
            column="42"
            startOffset="3452"
            endLine="96"
            endColumn="86"
            endOffset="3496"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/recommendation/PlaceWithImageAdapter.java"
            line="130"
            column="38"
            startOffset="4811"
            endLine="130"
            endColumn="63"
            endOffset="4836"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationActivity.java"
            line="1877"
            column="49"
            startOffset="73990"
            endLine="1877"
            endColumn="71"
            endOffset="74012"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ui/recommendation/RecommendationAdapter.java"
            line="108"
            column="39"
            startOffset="4035"
            endLine="108"
            endColumn="61"
            endOffset="4057"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1111"
            column="38"
            startOffset="40964"
            endLine="1111"
            endColumn="61"
            endOffset="40987"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1934"
            column="38"
            startOffset="75604"
            endLine="1934"
            endColumn="70"
            endOffset="75636"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1939"
            column="38"
            startOffset="75827"
            endLine="1939"
            endColumn="72"
            endOffset="75861"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1944"
            column="38"
            startOffset="76052"
            endLine="1944"
            endColumn="71"
            endOffset="76085"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="1949"
            column="39"
            startOffset="76278"
            endLine="1949"
            endColumn="72"
            endOffset="76311"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2468"
            column="43"
            startOffset="99514"
            endLine="2468"
            endColumn="66"
            endOffset="99537"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/ScheduleAddActivity.java"
            line="2473"
            column="39"
            startOffset="99674"
            endLine="2473"
            endColumn="71"
            endOffset="99706"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/ScheduleDetailAdapter.java"
            line="104"
            column="39"
            startOffset="3777"
            endLine="104"
            endColumn="66"
            endOffset="3804"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/ScheduleDetailAdapter.java"
            line="105"
            column="41"
            startOffset="3847"
            endLine="105"
            endColumn="70"
            endOffset="3876"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/schedule/adapter/ScheduleListAdapter.java"
            line="101"
            column="38"
            startOffset="3464"
            endLine="101"
            endColumn="87"
            endOffset="3513"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="164"
            column="27"
            startOffset="6371"
            endLine="164"
            endColumn="76"
            endOffset="6420"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="165"
            column="30"
            startOffset="6452"
            endLine="165"
            endColumn="71"
            endOffset="6493"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="166"
            column="35"
            startOffset="6530"
            endLine="166"
            endColumn="82"
            endOffset="6577"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="167"
            column="31"
            startOffset="6610"
            endLine="167"
            endColumn="76"
            endOffset="6655"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="168"
            column="30"
            startOffset="6687"
            endLine="168"
            endColumn="56"
            endOffset="6713"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="172"
            column="34"
            startOffset="6852"
            endLine="172"
            endColumn="61"
            endOffset="6879"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/ScheduleReminderDetailActivity.java"
            line="173"
            column="35"
            startOffset="6916"
            endLine="173"
            endColumn="63"
            endOffset="6944"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/TomorrowReminderAdapter.java"
            line="149"
            column="31"
            startOffset="4833"
            endLine="149"
            endColumn="80"
            endOffset="4882"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/TomorrowReminderAdapter.java"
            line="152"
            column="34"
            startOffset="4954"
            endLine="152"
            endColumn="75"
            endOffset="4995"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/timemate/features/home/<USER>/TomorrowReminderAdapter.java"
            line="155"
            column="39"
            startOffset="5092"
            endLine="155"
            endColumn="84"
            endOffset="5137"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;계정 전환&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="20"
            column="13"
            startOffset="699"
            endLine="20"
            endColumn="33"
            endOffset="719"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;사용할 계정을 선택하거나 새 계정을 만드세요\n계정별로 완전히 분리된 데이터를 가집니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="28"
            column="13"
            startOffset="979"
            endLine="28"
            endColumn="76"
            endOffset="1042"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔐 현재 계정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="54"
            column="17"
            startOffset="1913"
            endLine="54"
            endColumn="40"
            endOffset="1936"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;로그인되지 않음&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="64"
            column="17"
            startOffset="2321"
            endLine="64"
            endColumn="40"
            endOffset="2344"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;현재 계정으로 계속&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="73"
            column="17"
            startOffset="2691"
            endLine="73"
            endColumn="42"
            endOffset="2716"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👥 다른 계정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="91"
            column="13"
            startOffset="3222"
            endLine="91"
            endColumn="36"
            endOffset="3245"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;새 계정 만들기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml"
            line="120"
            column="13"
            startOffset="4202"
            endLine="120"
            endColumn="36"
            endOffset="4225"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="32"
            column="13"
            startOffset="1191"
            endLine="32"
            endColumn="33"
            endOffset="1211"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;새로운 친구를 추가해보세요!&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="77"
            column="21"
            startOffset="2789"
            endLine="77"
            endColumn="51"
            endOffset="2819"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구와 함께 일정을 공유하고 관리하세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="86"
            column="21"
            startOffset="3195"
            endLine="86"
            endColumn="57"
            endOffset="3231"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👤 친구 ID&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="111"
            column="25"
            startOffset="4294"
            endLine="111"
            endColumn="48"
            endOffset="4317"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 ID 입력&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="121"
            column="25"
            startOffset="4774"
            endLine="121"
            endColumn="48"
            endOffset="4797"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;✏️ 친구 닉네임&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="161"
            column="25"
            startOffset="6719"
            endLine="161"
            endColumn="49"
            endOffset="6743"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;닉네임 입력&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="171"
            column="25"
            startOffset="7206"
            endLine="171"
            endColumn="46"
            endOffset="7227"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💡 친구의 사용자 ID 찾는 방법:\n\n1. 친구에게 개인정보 화면을 열어달라고 요청\n2. 사용자 ID를 클릭하면 자동으로 복사됩니다\n3. 복사된 ID를 위에 입력하세요!&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="205"
            column="21"
            startOffset="8878"
            endLine="205"
            endColumn="135"
            endOffset="8992"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👥 친구 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml"
            line="218"
            column="17"
            startOffset="9481"
            endLine="218"
            endColumn="40"
            endOffset="9504"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 목록&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml"
            line="26"
            column="13"
            startOffset="1033"
            endLine="26"
            endColumn="33"
            endOffset="1053"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;등록된 친구&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml"
            line="53"
            column="13"
            startOffset="2040"
            endLine="53"
            endColumn="34"
            endOffset="2061"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👥&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml"
            line="78"
            column="17"
            startOffset="3009"
            endLine="78"
            endColumn="34"
            endOffset="3026"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;아직 친구가 없습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml"
            line="85"
            column="17"
            startOffset="3272"
            endLine="85"
            endColumn="43"
            endOffset="3298"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;+ 버튼을 눌러 친구를 추가해보세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml"
            line="95"
            column="17"
            startOffset="3698"
            endLine="95"
            endColumn="51"
            endOffset="3732"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml"
            line="125"
            column="9"
            startOffset="4947"
            endLine="125"
            endColumn="43"
            endOffset="4981"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;TimeMate&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="42"
            column="25"
            startOffset="1643"
            endLine="42"
            endColumn="48"
            endOffset="1666"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;--°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="86"
            column="33"
            startOffset="3737"
            endLine="86"
            endColumn="51"
            endOffset="3755"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;위치 확인 중...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="99"
            column="37"
            startOffset="4444"
            endLine="99"
            endColumn="62"
            endOffset="4469"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;날씨 정보 로딩 중...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="107"
            column="37"
            startOffset="4924"
            endLine="107"
            endColumn="65"
            endOffset="4952"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;체감 --°&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="126"
            column="29"
            startOffset="5670"
            endLine="126"
            endColumn="50"
            endOffset="5691"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;습도 --%&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="133"
            column="29"
            startOffset="6008"
            endLine="133"
            endColumn="50"
            endOffset="6029"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;오늘의 일정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="165"
            column="25"
            startOffset="7265"
            endLine="165"
            endColumn="46"
            endOffset="7286"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;12월 25일 (수)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="178"
            column="25"
            startOffset="7833"
            endLine="178"
            endColumn="51"
            endOffset="7859"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;오늘 예정된 일정이 없습니다 ✨&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="197"
            column="21"
            startOffset="8673"
            endLine="197"
            endColumn="53"
            endOffset="8705"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="239"
            column="25"
            startOffset="10528"
            endLine="239"
            endColumn="45"
            endOffset="10548"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 보기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="270"
            column="25"
            startOffset="11919"
            endLine="270"
            endColumn="45"
            endOffset="11939"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;내일 일정 미리보기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="331"
            column="29"
            startOffset="14615"
            endLine="331"
            endColumn="54"
            endOffset="14640"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;회의 참석&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="341"
            column="25"
            startOffset="15059"
            endLine="341"
            endColumn="45"
            endOffset="15079"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;집 → 회사&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="350"
            column="25"
            startOffset="15503"
            endLine="350"
            endColumn="46"
            endOffset="15524"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;시간: 09:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="365"
            column="29"
            startOffset="16241"
            endLine="365"
            endColumn="53"
            endOffset="16265"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;내일 일정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="374"
            column="29"
            startOffset="16716"
            endLine="374"
            endColumn="49"
            endOffset="16736"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;오늘의 OOTD 추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="412"
            column="25"
            startOffset="18306"
            endLine="412"
            endColumn="51"
            endOffset="18332"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;현재 날씨에 맞는 옷차림을 추천해드려요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="422"
            column="21"
            startOffset="18721"
            endLine="422"
            endColumn="57"
            endOffset="18757"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;아이디&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="9"
            column="9"
            startOffset="285"
            endLine="9"
            endColumn="27"
            endOffset="303"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;비밀번호&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="15"
            column="9"
            startOffset="464"
            endLine="15"
            endColumn="28"
            endOffset="483"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;로그인&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="23"
            column="9"
            startOffset="701"
            endLine="23"
            endColumn="27"
            endOffset="719"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔐 비밀번호를 잊으셨나요?&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="33"
            column="9"
            startOffset="1043"
            endLine="33"
            endColumn="39"
            endOffset="1073"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;취소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="53"
            column="13"
            startOffset="1721"
            endLine="53"
            endColumn="30"
            endOffset="1738"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;회원가입&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml"
            line="64"
            column="13"
            startOffset="2138"
            endLine="64"
            endColumn="32"
            endOffset="2157"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;알림함&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="30"
            column="13"
            startOffset="1110"
            endLine="30"
            endColumn="31"
            endOffset="1128"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔕&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="70"
            column="17"
            startOffset="2456"
            endLine="70"
            endColumn="34"
            endOffset="2473"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;새로운 알림이 없습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="77"
            column="17"
            startOffset="2719"
            endLine="77"
            endColumn="44"
            endOffset="2746"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 초대나 일정 공유 요청이 있으면\n여기에 표시됩니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml"
            line="86"
            column="17"
            startOffset="3090"
            endLine="86"
            endColumn="63"
            endOffset="3136"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 초대 알림&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="35"
            column="17"
            startOffset="1409"
            endLine="35"
            endColumn="40"
            endOffset="1432"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0개&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="44"
            column="17"
            startOffset="1783"
            endLine="44"
            endColumn="34"
            endOffset="1800"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;대기 중&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="64"
            column="17"
            startOffset="2476"
            endLine="64"
            endColumn="36"
            endOffset="2495"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;전체&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="74"
            column="17"
            startOffset="2877"
            endLine="74"
            endColumn="34"
            endOffset="2894"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📬&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="109"
            column="21"
            startOffset="4239"
            endLine="109"
            endColumn="38"
            endOffset="4256"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;받은 초대가 없습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="116"
            column="21"
            startOffset="4526"
            endLine="116"
            endColumn="47"
            endOffset="4552"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구들이 일정을 공유하면 여기에 표시됩니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml"
            line="124"
            column="21"
            startOffset="4890"
            endLine="124"
            endColumn="59"
            endOffset="4928"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔐&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="28"
            column="17"
            startOffset="963"
            endLine="28"
            endColumn="34"
            endOffset="980"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;비밀번호 찾기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="36"
            column="17"
            startOffset="1243"
            endLine="36"
            endColumn="39"
            endOffset="1265"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;아이디를 입력하여 비밀번호를 재설정하세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="46"
            column="17"
            startOffset="1624"
            endLine="46"
            endColumn="54"
            endOffset="1661"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;아이디&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="64"
            column="17"
            startOffset="2280"
            endLine="64"
            endColumn="35"
            endOffset="2298"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;새 비밀번호&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="82"
            column="17"
            startOffset="3104"
            endLine="82"
            endColumn="38"
            endOffset="3125"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;새 비밀번호 확인&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="101"
            column="17"
            startOffset="3990"
            endLine="101"
            endColumn="41"
            endOffset="4014"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📋 사용자 정보 확인&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="131"
            column="17"
            startOffset="5198"
            endLine="131"
            endColumn="44"
            endOffset="5225"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;취소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="156"
            column="17"
            startOffset="6061"
            endLine="156"
            endColumn="34"
            endOffset="6078"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;사용자 확인&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml"
            line="167"
            column="17"
            startOffset="6507"
            endLine="167"
            endColumn="38"
            endOffset="6528"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;뒤로가기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="26"
            column="13"
            startOffset="979"
            endLine="26"
            endColumn="46"
            endOffset="1012"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;프로필&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="33"
            column="13"
            startOffset="1223"
            endLine="33"
            endColumn="31"
            endOffset="1241"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;개인정보&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="78"
            column="13"
            startOffset="2725"
            endLine="78"
            endColumn="32"
            endOffset="2744"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;사용자 정보&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="98"
            column="13"
            startOffset="3374"
            endLine="98"
            endColumn="34"
            endOffset="3395"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;이름:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="112"
            column="17"
            startOffset="3882"
            endLine="112"
            endColumn="35"
            endOffset="3900"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;사용자&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="121"
            column="17"
            startOffset="4234"
            endLine="121"
            endColumn="35"
            endOffset="4252"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;사용자 ID:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="137"
            column="17"
            startOffset="4784"
            endLine="137"
            endColumn="39"
            endOffset="4806"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;user123&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="146"
            column="17"
            startOffset="5138"
            endLine="146"
            endColumn="39"
            endOffset="5160"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;이메일:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="166"
            column="17"
            startOffset="5906"
            endLine="166"
            endColumn="36"
            endOffset="5925"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;이메일 없음&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="175"
            column="17"
            startOffset="6260"
            endLine="175"
            endColumn="38"
            endOffset="6281"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;계정 관리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="197"
            column="13"
            startOffset="6939"
            endLine="197"
            endColumn="33"
            endOffset="6959"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔄 계정 전환&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="207"
            column="13"
            startOffset="7267"
            endLine="207"
            endColumn="36"
            endOffset="7290"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;로그아웃&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="215"
            column="13"
            startOffset="7557"
            endLine="215"
            endColumn="32"
            endOffset="7576"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;계정 삭제&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="223"
            column="13"
            startOffset="7858"
            endLine="223"
            endColumn="33"
            endOffset="7878"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;경로 설정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="242"
            column="13"
            startOffset="8467"
            endLine="242"
            endColumn="33"
            endOffset="8487"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;우선순위:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="257"
            column="17"
            startOffset="9001"
            endLine="257"
            endColumn="37"
            endOffset="9021"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;시간 우선&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="273"
            column="21"
            startOffset="9673"
            endLine="273"
            endColumn="41"
            endOffset="9693"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;비용 우선&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="282"
            column="21"
            startOffset="10064"
            endLine="282"
            endColumn="41"
            endOffset="10084"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;실시간 정보:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="298"
            column="17"
            startOffset="10531"
            endLine="298"
            endColumn="39"
            endOffset="10553"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;교통상황 반영&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml"
            line="308"
            column="17"
            startOffset="10948"
            endLine="308"
            endColumn="39"
            endOffset="10970"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;추천 장소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="32"
            column="21"
            startOffset="1284"
            endLine="32"
            endColumn="41"
            endOffset="1304"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📍 검색 위치&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="60"
            column="21"
            startOffset="2443"
            endLine="60"
            endColumn="44"
            endOffset="2466"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;지역을 입력하세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="69"
            column="21"
            startOffset="2864"
            endLine="69"
            endColumn="45"
            endOffset="2888"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🏷️ 카테고리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="92"
            column="21"
            startOffset="4024"
            endLine="92"
            endColumn="44"
            endOffset="4047"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🍽️ 맛집&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="109"
            column="25"
            startOffset="4840"
            endLine="109"
            endColumn="46"
            endOffset="4861"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;☕ 카페&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="130"
            column="25"
            startOffset="5999"
            endLine="130"
            endColumn="44"
            endOffset="6018"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🎯 놀거리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="151"
            column="25"
            startOffset="7162"
            endLine="151"
            endColumn="46"
            endOffset="7183"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🏨 숙소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="180"
            column="25"
            startOffset="8606"
            endLine="180"
            endColumn="45"
            endOffset="8626"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔍 주변 장소 검색&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="209"
            column="21"
            startOffset="10002"
            endLine="209"
            endColumn="47"
            endOffset="10028"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🗺️ 지도 보기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="253"
            column="29"
            startOffset="12007"
            endLine="253"
            endColumn="53"
            endOffset="12031"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;확대&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="263"
            column="29"
            startOffset="12526"
            endLine="263"
            endColumn="46"
            endOffset="12543"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;지도 로딩 중...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="298"
            column="33"
            startOffset="14238"
            endLine="298"
            endColumn="58"
            endOffset="14263"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🎯 추천 결과&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="341"
            column="29"
            startOffset="16088"
            endLine="341"
            endColumn="52"
            endOffset="16111"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0개 장소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="351"
            column="29"
            startOffset="16623"
            endLine="351"
            endColumn="49"
            endOffset="16643"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🔍&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="394"
            column="25"
            startOffset="18595"
            endLine="394"
            endColumn="42"
            endOffset="18612"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;위치를 입력하고 검색해보세요!&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="401"
            column="25"
            startOffset="18906"
            endLine="401"
            endColumn="56"
            endOffset="18937"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;주변의 맛집, 카페, 놀거리를 찾아드려요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml"
            line="412"
            column="25"
            startOffset="19469"
            endLine="412"
            endColumn="62"
            endOffset="19506"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;뒤로가기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="26"
            column="13"
            startOffset="992"
            endLine="26"
            endColumn="46"
            endOffset="1025"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;새 일정 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="32"
            column="13"
            startOffset="1192"
            endLine="32"
            endColumn="35"
            endOffset="1214"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="67"
            column="21"
            startOffset="2411"
            endLine="67"
            endColumn="41"
            endOffset="2431"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;예: 친구와 카페 가기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="76"
            column="21"
            startOffset="2805"
            endLine="76"
            endColumn="48"
            endOffset="2832"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;날짜 및 시간&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="97"
            column="21"
            startOffset="3697"
            endLine="97"
            endColumn="43"
            endOffset="3719"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;날짜 선택&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="112"
            column="25"
            startOffset="4368"
            endLine="112"
            endColumn="45"
            endOffset="4388"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;시간 선택&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="121"
            column="25"
            startOffset="4789"
            endLine="121"
            endColumn="45"
            endOffset="4809"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;날짜와 시간을 선택해주세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="131"
            column="21"
            startOffset="5194"
            endLine="131"
            endColumn="50"
            endOffset="5223"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지 → 도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="152"
            column="21"
            startOffset="6060"
            endLine="152"
            endColumn="45"
            endOffset="6084"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지를 입력하세요 (2글자 이상)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="161"
            column="25"
            startOffset="6508"
            endLine="161"
            endColumn="59"
            endOffset="6542"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도착지를 입력하세요 (2글자 이상)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="190"
            column="25"
            startOffset="8078"
            endLine="190"
            endColumn="59"
            endOffset="8112"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;길찾기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="220"
            column="25"
            startOffset="9672"
            endLine="220"
            endColumn="43"
            endOffset="9690"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;✅ 선택된 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="247"
            column="33"
            startOffset="11053"
            endLine="247"
            endColumn="56"
            endOffset="11076"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;경로 정보가 여기에 표시됩니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="258"
            column="33"
            startOffset="11686"
            endLine="258"
            endColumn="64"
            endOffset="11717"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;함께할 친구&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="290"
            column="25"
            startOffset="13042"
            endLine="290"
            endColumn="46"
            endOffset="13063"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 선택&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="298"
            column="25"
            startOffset="13428"
            endLine="298"
            endColumn="45"
            endOffset="13448"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;선택된 친구가 없습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="307"
            column="21"
            startOffset="13774"
            endLine="307"
            endColumn="48"
            endOffset="13801"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="327"
            column="21"
            startOffset="14591"
            endLine="327"
            endColumn="38"
            endOffset="14608"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정에 대한 메모를 입력하세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="336"
            column="21"
            startOffset="14988"
            endLine="336"
            endColumn="52"
            endOffset="15019"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;취소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="364"
            column="13"
            startOffset="15933"
            endLine="364"
            endColumn="30"
            endOffset="15950"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;저장&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml"
            line="373"
            column="13"
            startOffset="16257"
            endLine="373"
            endColumn="30"
            endOffset="16274"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;오늘의 일정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_calendar.xml"
            line="62"
            column="13"
            startOffset="2554"
            endLine="62"
            endColumn="34"
            endOffset="2575"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;선택한 날짜에 일정이 없습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_calendar.xml"
            line="93"
            column="17"
            startOffset="3636"
            endLine="93"
            endColumn="48"
            endOffset="3667"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;+ 버튼을 눌러 새 일정을 추가해보세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_calendar.xml"
            line="101"
            column="17"
            startOffset="3960"
            endLine="101"
            endColumn="53"
            endOffset="3996"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_calendar.xml"
            line="118"
            column="9"
            startOffset="4553"
            endLine="118"
            endColumn="43"
            endOffset="4587"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 관리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="27"
            column="17"
            startOffset="1057"
            endLine="27"
            endColumn="37"
            endOffset="1077"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;‹&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="57"
            column="21"
            startOffset="2209"
            endLine="57"
            endColumn="37"
            endOffset="2225"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024년 1월&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="69"
            column="21"
            startOffset="2762"
            endLine="69"
            endColumn="44"
            endOffset="2785"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;›&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="79"
            column="21"
            startOffset="3186"
            endLine="79"
            endColumn="37"
            endOffset="3202"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;• 일정이 있는 날짜를 터치하면 상세 정보를 볼 수 있습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="97"
            column="17"
            startOffset="3905"
            endLine="97"
            endColumn="65"
            endOffset="3953"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;등록된 일정이 없습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="134"
            column="21"
            startOffset="5276"
            endLine="134"
            endColumn="48"
            endOffset="5303"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;+ 버튼을 눌러 일정을 추가해보세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="145"
            column="21"
            startOffset="5796"
            endLine="145"
            endColumn="55"
            endOffset="5830"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml"
            line="175"
            column="9"
            startOffset="6999"
            endLine="175"
            endColumn="43"
            endOffset="7033"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 알림&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="30"
            column="13"
            startOffset="1102"
            endLine="30"
            endColumn="33"
            endOffset="1122"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="72"
            column="25"
            startOffset="2602"
            endLine="72"
            endColumn="45"
            endOffset="2622"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024년 1월 1일 10:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="83"
            column="25"
            startOffset="3126"
            endLine="83"
            endColumn="57"
            endOffset="3158"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지 → 도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="93"
            column="25"
            startOffset="3611"
            endLine="93"
            endColumn="49"
            endOffset="3635"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚗 추천 출발 정보&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="120"
            column="25"
            startOffset="4709"
            endLine="120"
            endColumn="51"
            endOffset="4735"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;추천 출발시간: 09:30&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="130"
            column="25"
            startOffset="5195"
            endLine="130"
            endColumn="54"
            endOffset="5224"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;예상 30분 소요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="140"
            column="25"
            startOffset="5681"
            endLine="140"
            endColumn="49"
            endOffset="5705"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;교통수단: 자동차&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="149"
            column="25"
            startOffset="6113"
            endLine="149"
            endColumn="49"
            endOffset="6137"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;거리: 15.2 km&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="158"
            column="25"
            startOffset="6544"
            endLine="158"
            endColumn="51"
            endOffset="6570"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;💰 예상 비용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="184"
            column="25"
            startOffset="7610"
            endLine="184"
            endColumn="48"
            endOffset="7633"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;통행료: 2,500원&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="200"
            column="29"
            startOffset="8367"
            endLine="200"
            endColumn="55"
            endOffset="8393"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;연료비: 3,200원&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="209"
            column="29"
            startOffset="8816"
            endLine="209"
            endColumn="55"
            endOffset="8842"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🗺️ 길찾기 시작&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="237"
            column="13"
            startOffset="9628"
            endLine="237"
            endColumn="38"
            endOffset="9653"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⏰ 10분 후&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="255"
            column="17"
            startOffset="10292"
            endLine="255"
            endColumn="39"
            endOffset="10314"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;✓ 확인&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml"
            line="266"
            column="17"
            startOffset="10727"
            endLine="266"
            endColumn="36"
            endOffset="10746"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;이름&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="14"
            column="13"
            startOffset="455"
            endLine="14"
            endColumn="30"
            endOffset="472"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;이메일&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="20"
            column="13"
            startOffset="650"
            endLine="20"
            endColumn="31"
            endOffset="668"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;전화번호&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="27"
            column="13"
            startOffset="896"
            endLine="27"
            endColumn="32"
            endOffset="915"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;남&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="40"
            column="17"
            startOffset="1364"
            endLine="40"
            endColumn="33"
            endOffset="1380"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;여&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="46"
            column="17"
            startOffset="1583"
            endLine="46"
            endColumn="33"
            endOffset="1599"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;로그인 ID&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="53"
            column="13"
            startOffset="1809"
            endLine="53"
            endColumn="34"
            endOffset="1830"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;비밀번호&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="59"
            column="13"
            startOffset="2011"
            endLine="59"
            endColumn="32"
            endOffset="2030"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;취소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="74"
            column="17"
            startOffset="2532"
            endLine="74"
            endColumn="34"
            endOffset="2549"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;회원가입&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml"
            line="85"
            column="17"
            startOffset="2987"
            endLine="85"
            endColumn="36"
            endOffset="3006"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;홈&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml"
            line="5"
            column="9"
            startOffset="162"
            endLine="5"
            endColumn="26"
            endOffset="179"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml"
            line="9"
            column="9"
            startOffset="284"
            endLine="9"
            endColumn="27"
            endOffset="302"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구목록&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml"
            line="13"
            column="9"
            startOffset="410"
            endLine="13"
            endColumn="29"
            endOffset="430"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml"
            line="17"
            column="9"
            startOffset="544"
            endLine="17"
            endColumn="27"
            endOffset="562"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;프로필&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml"
            line="21"
            column="9"
            startOffset="674"
            endLine="21"
            endColumn="28"
            endOffset="693"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;경로 선택&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="31"
            column="13"
            startOffset="1214"
            endLine="31"
            endColumn="33"
            endOffset="1234"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;닫기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="44"
            column="13"
            startOffset="1725"
            endLine="44"
            endColumn="44"
            endOffset="1756"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;서울역 → 강남역&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="62"
            column="13"
            startOffset="2324"
            endLine="62"
            endColumn="37"
            endOffset="2348"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;3개 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="72"
            column="13"
            startOffset="2707"
            endLine="72"
            endColumn="33"
            endOffset="2727"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;취소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="104"
            column="13"
            startOffset="3826"
            endLine="104"
            endColumn="30"
            endOffset="3843"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정에 저장&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml"
            line="116"
            column="13"
            startOffset="4343"
            endLine="116"
            endColumn="34"
            endOffset="4364"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👥 함께할 친구&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_friend_selection.xml"
            line="28"
            column="17"
            startOffset="1050"
            endLine="28"
            endColumn="41"
            endOffset="1074"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정에 초대할 친구들을 선택하세요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_friend_selection.xml"
            line="38"
            column="17"
            startOffset="1474"
            endLine="38"
            endColumn="50"
            endOffset="1507"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;0명 선택됨&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_friend_selection.xml"
            line="60"
            column="13"
            startOffset="2302"
            endLine="60"
            endColumn="34"
            endOffset="2323"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;취소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_friend_selection.xml"
            line="79"
            column="17"
            startOffset="3001"
            endLine="79"
            endColumn="34"
            endOffset="3018"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;확인&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_friend_selection.xml"
            line="91"
            column="17"
            startOffset="3498"
            endLine="91"
            endColumn="34"
            endOffset="3515"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🗺️ 경로 선택&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="28"
            column="17"
            startOffset="1023"
            endLine="28"
            endColumn="41"
            endOffset="1047"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지 → 도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="39"
            column="17"
            startOffset="1486"
            endLine="39"
            endColumn="41"
            endOffset="1510"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚌&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="99"
            column="29"
            startOffset="3928"
            endLine="99"
            endColumn="46"
            endOffset="3945"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;대중교통&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="108"
            column="29"
            startOffset="4359"
            endLine="108"
            endColumn="48"
            endOffset="4378"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⭐ 추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="118"
            column="29"
            startOffset="4882"
            endLine="118"
            endColumn="48"
            endOffset="4901"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;지하철/버스 (환승 포함)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="141"
            column="29"
            startOffset="5991"
            endLine="141"
            endColumn="58"
            endOffset="6020"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;22분 (1,600원)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="151"
            column="29"
            startOffset="6518"
            endLine="151"
            endColumn="56"
            endOffset="6545"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚗&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="199"
            column="29"
            startOffset="8589"
            endLine="199"
            endColumn="46"
            endOffset="8606"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;자동차&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="208"
            column="29"
            startOffset="9020"
            endLine="208"
            endColumn="47"
            endOffset="9038"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⭐ 추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="218"
            column="29"
            startOffset="9543"
            endLine="218"
            endColumn="48"
            endOffset="9562"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;최단거리 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="241"
            column="29"
            startOffset="10653"
            endLine="241"
            endColumn="51"
            endOffset="10675"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;18분 (4,100원)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="251"
            column="29"
            startOffset="11174"
            endLine="251"
            endColumn="56"
            endOffset="11201"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚴&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="299"
            column="29"
            startOffset="13245"
            endLine="299"
            endColumn="46"
            endOffset="13262"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;자전거&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="308"
            column="29"
            startOffset="13676"
            endLine="308"
            endColumn="47"
            endOffset="13694"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⭐ 추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="318"
            column="29"
            startOffset="14199"
            endLine="318"
            endColumn="48"
            endOffset="14218"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;자전거 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="341"
            column="29"
            startOffset="15309"
            endLine="341"
            endColumn="50"
            endOffset="15330"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;25분 (무료)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="351"
            column="29"
            startOffset="15829"
            endLine="351"
            endColumn="52"
            endOffset="15852"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚶&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="399"
            column="29"
            startOffset="17895"
            endLine="399"
            endColumn="46"
            endOffset="17912"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도보&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="408"
            column="29"
            startOffset="18326"
            endLine="408"
            endColumn="46"
            endOffset="18343"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⭐ 추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="418"
            column="29"
            startOffset="18848"
            endLine="418"
            endColumn="48"
            endOffset="18867"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도보 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="441"
            column="29"
            startOffset="19958"
            endLine="441"
            endColumn="49"
            endOffset="19978"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;1시간 38분 (무료)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="451"
            column="29"
            startOffset="20477"
            endLine="451"
            endColumn="56"
            endOffset="20504"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚕&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="499"
            column="29"
            startOffset="22544"
            endLine="499"
            endColumn="46"
            endOffset="22561"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;택시&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="508"
            column="29"
            startOffset="22975"
            endLine="508"
            endColumn="46"
            endOffset="22992"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⭐ 추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="518"
            column="29"
            startOffset="23494"
            endLine="518"
            endColumn="48"
            endOffset="23513"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;택시 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="541"
            column="29"
            startOffset="24601"
            endLine="541"
            endColumn="49"
            endOffset="24621"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;15분 (8,200원)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="551"
            column="29"
            startOffset="25117"
            endLine="551"
            endColumn="56"
            endOffset="25144"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;취소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="581"
            column="17"
            startOffset="26119"
            endLine="581"
            endColumn="34"
            endOffset="26136"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;선택&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml"
            line="593"
            column="17"
            startOffset="26604"
            endLine="593"
            endColumn="34"
            endOffset="26621"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024년 1월 15일&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
            line="23"
            column="13"
            startOffset="820"
            endLine="23"
            endColumn="40"
            endOffset="847"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 추가&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
            line="70"
            column="13"
            startOffset="2303"
            endLine="70"
            endColumn="33"
            endOffset="2323"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;전체 보기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml"
            line="79"
            column="13"
            startOffset="2632"
            endLine="79"
            endColumn="33"
            endOffset="2652"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="39"
            column="17"
            startOffset="1480"
            endLine="39"
            endColumn="37"
            endOffset="1500"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024년 1월 15일 14:30&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="83"
            column="21"
            startOffset="3190"
            endLine="83"
            endColumn="54"
            endOffset="3223"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="107"
            column="21"
            startOffset="4118"
            endLine="107"
            endColumn="40"
            endOffset="4137"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;서울역&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="118"
            column="21"
            startOffset="4600"
            endLine="118"
            endColumn="39"
            endOffset="4618"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도착지:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="142"
            column="21"
            startOffset="5509"
            endLine="142"
            endColumn="40"
            endOffset="5528"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;강남역&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="153"
            column="21"
            startOffset="5993"
            endLine="153"
            endColumn="39"
            endOffset="6011"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="179"
            column="21"
            startOffset="6967"
            endLine="179"
            endColumn="39"
            endOffset="6985"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;중요한 회의입니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="190"
            column="21"
            startOffset="7443"
            endLine="190"
            endColumn="45"
            endOffset="7467"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;✏️ 수정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="210"
            column="17"
            startOffset="8073"
            endLine="210"
            endColumn="37"
            endOffset="8093"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🗑️ 삭제&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml"
            line="221"
            column="17"
            startOffset="8514"
            endLine="221"
            endColumn="38"
            endOffset="8535"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="38"
            column="21"
            startOffset="1446"
            endLine="38"
            endColumn="41"
            endOffset="1466"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024년 1월 15일&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="51"
            column="21"
            startOffset="2029"
            endLine="51"
            endColumn="48"
            endOffset="2056"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 시간&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="96"
            column="25"
            startOffset="3755"
            endLine="96"
            endColumn="45"
            endOffset="3775"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;오후 2:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="106"
            column="25"
            startOffset="4249"
            endLine="106"
            endColumn="47"
            endOffset="4271"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;위치 정보&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="127"
            column="25"
            startOffset="5181"
            endLine="127"
            endColumn="45"
            endOffset="5201"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="143"
            column="29"
            startOffset="5930"
            endLine="143"
            endColumn="46"
            endOffset="5947"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="153"
            column="29"
            startOffset="6444"
            endLine="153"
            endColumn="47"
            endOffset="6462"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도착&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="169"
            column="29"
            startOffset="7127"
            endLine="169"
            endColumn="46"
            endOffset="7144"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="179"
            column="29"
            startOffset="7643"
            endLine="179"
            endColumn="47"
            endOffset="7661"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;상태&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="200"
            column="25"
            startOffset="8509"
            endLine="200"
            endColumn="42"
            endOffset="8526"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;진행중&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="210"
            column="25"
            startOffset="8994"
            endLine="210"
            endColumn="43"
            endOffset="9012"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="231"
            column="25"
            startOffset="9904"
            endLine="231"
            endColumn="42"
            endOffset="9921"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모 내용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="241"
            column="25"
            startOffset="10387"
            endLine="241"
            endColumn="45"
            endOffset="10407"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;선택된 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="263"
            column="25"
            startOffset="11362"
            endLine="263"
            endColumn="46"
            endOffset="11383"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;경로 정보&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="273"
            column="25"
            startOffset="11854"
            endLine="273"
            endColumn="45"
            endOffset="11874"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;함께하는 친구&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="295"
            column="25"
            startOffset="12829"
            endLine="295"
            endColumn="47"
            endOffset="12851"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 목록&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="305"
            column="25"
            startOffset="13320"
            endLine="305"
            endColumn="45"
            endOffset="13340"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;수정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="332"
            column="17"
            startOffset="14271"
            endLine="332"
            endColumn="34"
            endOffset="14288"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;삭제&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml"
            line="347"
            column="17"
            startOffset="14913"
            endLine="347"
            endColumn="34"
            endOffset="14930"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;사용자 닉네임&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_account.xml"
            line="47"
            column="17"
            startOffset="1623"
            endLine="47"
            endColumn="39"
            endOffset="1645"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;ID: user123&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_account.xml"
            line="57"
            column="17"
            startOffset="2025"
            endLine="57"
            endColumn="43"
            endOffset="2051"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;<EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_account.xml"
            line="66"
            column="17"
            startOffset="2391"
            endLine="66"
            endColumn="48"
            endOffset="2422"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;가입일: 2024.01.01&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_account.xml"
            line="75"
            column="17"
            startOffset="2763"
            endLine="75"
            endColumn="47"
            endOffset="2793"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 이름&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend.xml"
            line="45"
            column="17"
            startOffset="1770"
            endLine="45"
            endColumn="37"
            endOffset="1790"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;수락&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend.xml"
            line="98"
            column="21"
            startOffset="3903"
            endLine="98"
            endColumn="38"
            endOffset="3920"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;거절&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend.xml"
            line="109"
            column="21"
            startOffset="4441"
            endLine="109"
            endColumn="38"
            endOffset="4458"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👤&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend_selection.xml"
            line="25"
            column="13"
            startOffset="909"
            endLine="25"
            endColumn="30"
            endOffset="926"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구 이름&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_friend_selection.xml"
            line="43"
            column="17"
            startOffset="1580"
            endLine="43"
            endColumn="37"
            endOffset="1600"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;회의 참석&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_home_schedule.xml"
            line="33"
            column="13"
            startOffset="1151"
            endLine="33"
            endColumn="33"
            endOffset="1171"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⏰ 09:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_home_schedule.xml"
            line="43"
            column="13"
            startOffset="1525"
            endLine="43"
            endColumn="35"
            endOffset="1547"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📍 회사 회의실&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_home_schedule.xml"
            line="52"
            column="13"
            startOffset="1883"
            endLine="52"
            endColumn="37"
            endOffset="1907"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📅 일정 초대&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="44"
            column="21"
            startOffset="1696"
            endLine="44"
            endColumn="44"
            endOffset="1719"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;방금 전&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="53"
            column="21"
            startOffset="2100"
            endLine="53"
            endColumn="40"
            endOffset="2119"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;친구님이 일정에 초대했습니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="67"
            column="13"
            startOffset="2554"
            endLine="67"
            endColumn="43"
            endOffset="2584"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;거절&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="86"
            column="17"
            startOffset="3321"
            endLine="86"
            endColumn="34"
            endOffset="3338"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;수락&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml"
            line="98"
            column="17"
            startOffset="3860"
            endLine="98"
            endColumn="34"
            endOffset="3877"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;👗 스타일&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_ootd_recommendation.xml"
            line="40"
            column="17"
            startOffset="1556"
            endLine="40"
            endColumn="38"
            endOffset="1577"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;스타일 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_ootd_recommendation.xml"
            line="60"
            column="17"
            startOffset="2210"
            endLine="60"
            endColumn="38"
            endOffset="2231"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;스타일 설명이 여기에 표시됩니다&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_ootd_recommendation.xml"
            line="75"
            column="17"
            startOffset="2809"
            endLine="75"
            endColumn="49"
            endOffset="2841"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;#트렌디 #편안함 #데일리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_ootd_recommendation.xml"
            line="88"
            column="17"
            startOffset="3332"
            endLine="88"
            endColumn="46"
            endOffset="3361"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;장소명&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_autocomplete.xml"
            line="14"
            column="9"
            startOffset="489"
            endLine="14"
            endColumn="27"
            endOffset="507"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;주소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_autocomplete.xml"
            line="27"
            column="9"
            startOffset="896"
            endLine="27"
            endColumn="26"
            endOffset="913"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;카테고리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_autocomplete.xml"
            line="39"
            column="9"
            startOffset="1278"
            endLine="39"
            endColumn="28"
            endOffset="1297"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;장소명&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggest.xml"
            line="37"
            column="17"
            startOffset="1301"
            endLine="37"
            endColumn="35"
            endOffset="1319"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;주소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggest.xml"
            line="49"
            column="17"
            startOffset="1756"
            endLine="49"
            endColumn="34"
            endOffset="1773"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;카테고리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggest.xml"
            line="63"
            column="13"
            startOffset="2222"
            endLine="63"
            endColumn="32"
            endOffset="2241"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;장소명&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggestion.xml"
            line="31"
            column="13"
            startOffset="1036"
            endLine="31"
            endColumn="31"
            endOffset="1054"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;주소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggestion.xml"
            line="44"
            column="13"
            startOffset="1486"
            endLine="44"
            endColumn="30"
            endOffset="1503"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;카테고리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggestion.xml"
            line="56"
            column="13"
            startOffset="1907"
            endLine="56"
            endColumn="32"
            endOffset="1926"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;장소 이미지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="32"
            column="17"
            startOffset="1218"
            endLine="32"
            endColumn="52"
            endOffset="1253"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;로딩중&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="53"
            column="21"
            startOffset="2089"
            endLine="53"
            endColumn="39"
            endOffset="2107"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;장소명&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="81"
            column="21"
            startOffset="3080"
            endLine="81"
            endColumn="39"
            endOffset="3098"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;500m&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="91"
            column="21"
            startOffset="3518"
            endLine="91"
            endColumn="40"
            endOffset="3537"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;카테고리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="107"
            column="17"
            startOffset="4177"
            endLine="107"
            endColumn="36"
            endOffset="4196"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;주소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="117"
            column="17"
            startOffset="4575"
            endLine="117"
            endColumn="34"
            endOffset="4592"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;전화번호&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="129"
            column="17"
            startOffset="5049"
            endLine="129"
            endColumn="36"
            endOffset="5068"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;상세보기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml"
            line="146"
            column="13"
            startOffset="5689"
            endLine="146"
            endColumn="46"
            endOffset="5722"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🏪&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="47"
            column="17"
            startOffset="1836"
            endLine="47"
            endColumn="34"
            endOffset="1853"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;장소명&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="65"
            column="17"
            startOffset="2402"
            endLine="65"
            endColumn="35"
            endOffset="2420"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;카테고리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="79"
            column="17"
            startOffset="2977"
            endLine="79"
            endColumn="36"
            endOffset="2996"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;주소&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="90"
            column="17"
            startOffset="3430"
            endLine="90"
            endColumn="34"
            endOffset="3447"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;⭐ 4.5&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="109"
            column="21"
            startOffset="4220"
            endLine="109"
            endColumn="41"
            endOffset="4240"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📍 500m&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="122"
            column="21"
            startOffset="4854"
            endLine="122"
            endColumn="43"
            endOffset="4876"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;길찾기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml"
            line="140"
            column="21"
            startOffset="5674"
            endLine="140"
            endColumn="39"
            endOffset="5692"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;추천 최적 경로&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="44"
            column="17"
            startOffset="1869"
            endLine="44"
            endColumn="40"
            endOffset="1892"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;교통수단&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="55"
            column="17"
            startOffset="2379"
            endLine="55"
            endColumn="50"
            endOffset="2412"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="72"
            column="17"
            startOffset="2972"
            endLine="72"
            endColumn="35"
            endOffset="2990"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot; → &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="83"
            column="17"
            startOffset="3437"
            endLine="83"
            endColumn="35"
            endOffset="3455"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="93"
            column="17"
            startOffset="3896"
            endLine="93"
            endColumn="35"
            endOffset="3914"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;3.2 km&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="115"
            column="17"
            startOffset="4744"
            endLine="115"
            endColumn="38"
            endOffset="4765"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;25분&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="131"
            column="17"
            startOffset="5460"
            endLine="131"
            endColumn="35"
            endOffset="5478"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;3,200원&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="146"
            column="17"
            startOffset="6041"
            endLine="146"
            endColumn="38"
            endOffset="6062"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;15분&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="172"
            column="17"
            startOffset="7040"
            endLine="172"
            endColumn="35"
            endOffset="7058"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;10분&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml"
            line="186"
            column="17"
            startOffset="7662"
            endLine="186"
            endColumn="35"
            endOffset="7680"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;대중교통&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="41"
            column="17"
            startOffset="1449"
            endLine="41"
            endColumn="36"
            endOffset="1468"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;지하철 + 버스 이용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="51"
            column="17"
            startOffset="1831"
            endLine="51"
            endColumn="43"
            endOffset="1857"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="63"
            column="13"
            startOffset="2243"
            endLine="63"
            endColumn="30"
            endOffset="2260"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;시간&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="90"
            column="17"
            startOffset="3119"
            endLine="90"
            endColumn="34"
            endOffset="3136"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;25분&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="98"
            column="17"
            startOffset="3429"
            endLine="98"
            endColumn="35"
            endOffset="3447"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;거리&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="124"
            column="17"
            startOffset="4279"
            endLine="124"
            endColumn="34"
            endOffset="4296"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;3.2km&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="132"
            column="17"
            startOffset="4589"
            endLine="132"
            endColumn="37"
            endOffset="4609"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;비용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="158"
            column="17"
            startOffset="5441"
            endLine="158"
            endColumn="34"
            endOffset="5458"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;1,500원&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="166"
            column="17"
            startOffset="5747"
            endLine="166"
            endColumn="38"
            endOffset="5768"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;지하철과 버스를 이용한 최적 경로입니다.&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml"
            line="181"
            column="9"
            startOffset="6179"
            endLine="181"
            endColumn="46"
            endOffset="6216"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule.xml"
            line="34"
            column="17"
            startOffset="1338"
            endLine="34"
            endColumn="37"
            endOffset="1358"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;날짜 및 시간&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule.xml"
            line="47"
            column="17"
            startOffset="1882"
            endLine="47"
            endColumn="39"
            endOffset="1904"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지 → 도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule.xml"
            line="57"
            column="17"
            startOffset="2308"
            endLine="57"
            endColumn="41"
            endOffset="2332"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule.xml"
            line="70"
            column="17"
            startOffset="2853"
            endLine="70"
            endColumn="34"
            endOffset="2870"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 수정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule.xml"
            line="100"
            column="17"
            startOffset="3988"
            endLine="100"
            endColumn="51"
            endOffset="4022"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;회의 참석&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="14"
            column="9"
            startOffset="487"
            endLine="14"
            endColumn="29"
            endOffset="507"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;오전 10:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="39"
            column="13"
            startOffset="1326"
            endLine="39"
            endColumn="36"
            endOffset="1349"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="72"
            column="17"
            startOffset="2462"
            endLine="72"
            endColumn="35"
            endOffset="2480"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="95"
            column="17"
            startOffset="3274"
            endLine="95"
            endColumn="35"
            endOffset="3292"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;함께하는 친구들&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="124"
            column="13"
            startOffset="4172"
            endLine="124"
            endColumn="36"
            endOffset="4195"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="142"
            column="13"
            startOffset="4716"
            endLine="142"
            endColumn="30"
            endOffset="4733"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 메모 내용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="152"
            column="13"
            startOffset="5075"
            endLine="152"
            endColumn="36"
            endOffset="5098"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;수정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="173"
            column="13"
            startOffset="5757"
            endLine="173"
            endColumn="30"
            endOffset="5774"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;삭제&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml"
            line="182"
            column="13"
            startOffset="6090"
            endLine="182"
            endColumn="30"
            endOffset="6107"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;오늘의 일정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_header.xml"
            line="15"
            column="9"
            startOffset="514"
            endLine="15"
            endColumn="30"
            endOffset="535"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;3개&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_header.xml"
            line="23"
            column="9"
            startOffset="783"
            endLine="23"
            endColumn="26"
            endOffset="800"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="55"
            column="21"
            startOffset="2062"
            endLine="55"
            endColumn="41"
            endOffset="2082"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024년 1월 1일 10:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="65"
            column="21"
            startOffset="2510"
            endLine="65"
            endColumn="53"
            endOffset="2542"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="102"
            column="17"
            startOffset="3845"
            endLine="102"
            endColumn="35"
            endOffset="3863"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="125"
            column="17"
            startOffset="4741"
            endLine="125"
            endColumn="35"
            endOffset="4759"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모 내용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="136"
            column="13"
            startOffset="5094"
            endLine="136"
            endColumn="33"
            endOffset="5114"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;개인 일정&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="157"
            column="17"
            startOffset="5890"
            endLine="157"
            endColumn="37"
            endOffset="5910"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;길찾기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml"
            line="169"
            column="17"
            startOffset="6387"
            endLine="169"
            endColumn="35"
            endOffset="6405"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="38"
            column="17"
            startOffset="1334"
            endLine="38"
            endColumn="37"
            endOffset="1354"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024-01-15 09:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="51"
            column="17"
            startOffset="1840"
            endLine="51"
            endColumn="48"
            endOffset="1871"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지 → 도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="64"
            column="17"
            startOffset="2398"
            endLine="64"
            endColumn="41"
            endOffset="2422"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모 내용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="79"
            column="17"
            startOffset="3037"
            endLine="79"
            endColumn="37"
            endOffset="3057"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;편집&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml"
            line="97"
            column="13"
            startOffset="3705"
            endLine="97"
            endColumn="44"
            endOffset="3736"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;09:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="36"
            column="17"
            startOffset="1314"
            endLine="36"
            endColumn="37"
            endOffset="1334"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;일정 제목&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="49"
            column="13"
            startOffset="1735"
            endLine="49"
            endColumn="33"
            endOffset="1755"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;출발지 → 도착지&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="76"
            column="17"
            startOffset="2728"
            endLine="76"
            endColumn="41"
            endOffset="2752"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;메모 내용&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml"
            line="90"
            column="13"
            startOffset="3203"
            endLine="90"
            endColumn="33"
            endOffset="3223"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;회의 약속&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="23"
            column="13"
            startOffset="804"
            endLine="23"
            endColumn="33"
            endOffset="824"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024-01-15 14:00&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="34"
            column="13"
            startOffset="1186"
            endLine="34"
            endColumn="44"
            endOffset="1217"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;집 → 회사&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="44"
            column="13"
            startOffset="1540"
            endLine="44"
            endColumn="34"
            endOffset="1561"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;예상 25분 소요&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="54"
            column="13"
            startOffset="1889"
            endLine="54"
            endColumn="37"
            endOffset="1913"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;추천 출발: 13:25&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="64"
            column="13"
            startOffset="2254"
            endLine="64"
            endColumn="40"
            endOffset="2281"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🚇 지하철&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="75"
            column="13"
            startOffset="2638"
            endLine="75"
            endColumn="34"
            endOffset="2659"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;상세보기&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="91"
            column="17"
            startOffset="3200"
            endLine="91"
            endColumn="36"
            endOffset="3219"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;확인&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml"
            line="103"
            column="17"
            startOffset="3696"
            endLine="103"
            endColumn="34"
            endOffset="3713"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🌅 내일 출발 추천&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_header.xml"
            line="12"
            column="9"
            startOffset="411"
            endLine="12"
            endColumn="35"
            endOffset="437"/>
    </incident>

</incidents>
