<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5">

    <!-- 프로필과 같은 헤더 스타일 -->
    <LinearLayout
        android:id="@+id/layoutHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:paddingTop="56dp"
        android:gravity="center_vertical"
        android:background="@android:color/white"
        android:elevation="2dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="친구 목록"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:fontFamily="@font/pretendard_bold"
            android:gravity="center" />

    </LinearLayout>

    <!-- 친구 리스트 -->
    <LinearLayout
        android:id="@+id/layoutFriendList"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="16dp"
        android:background="@android:color/white"
        android:elevation="4dp"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/layoutHeader"
        app:layout_constraintBottom_toTopOf="@id/bottomNavigationView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="등록된 친구"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerFriends"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="80dp" />

        <!-- 💙 블루 톤 Empty State -->
        <LinearLayout
            android:id="@+id/layoutEmptyState"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            android:background="@color/background">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="👥"
                android:textSize="48sp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="아직 친구가 없습니다"
                android:textSize="18sp"
                android:textColor="@color/text_primary"
                android:textStyle="bold"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="+ 버튼을 눌러 친구를 추가해보세요"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:fontFamily="sans-serif-light" />

        </LinearLayout>

    </LinearLayout>

    <!-- 바텀 네비게이션 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigationView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        style="@style/TimeMateBottomNavigation"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:menu="@menu/bottom_nav_menu"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- iOS 스타일 FAB 친구 추가 버튼 (바텀 네비게이션과 충분한 간격) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddFriend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/card_margin"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="친구 추가"
        app:backgroundTint="@color/accent"
        app:tint="@color/white"
        app:elevation="@dimen/card_elevation_selected"
        app:layout_constraintBottom_toTopOf="@id/bottomNavigationView"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
