http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_in.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/anim/fade_out.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/animator/card_elevation_animator.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_color.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/color/ios_category_text_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_primary.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/timemate_edittext_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_walking.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_selected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/timemate_button_primary.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/route_normal_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_transit.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_distance_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/status_badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_back.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/route_recommended_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_tab_selected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_friends.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_dot.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/indicator_dot_inactive.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_secondary_ios.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_schedule_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_time.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_map_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_error.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_completed.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_card_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bg_tag_rounded.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/recommended_badge.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_reject.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_access_time.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_transit.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/notification_badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/modal_handle.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_car.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/badge_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_search_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_home.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_count_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_cancel.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_close.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_bus.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/timemate_button_outlined.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_end.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_chip_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/category_tag_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_driving.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_snooze.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_overdue.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/circle_background_ios.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_forward.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_edit.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/weather_card_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_header_blue_gradient.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_start.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notifications.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_phone.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/bottom_sheet_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/card_normal.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_weather.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_image_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_arrow_right.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_directions_walk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_tab_container.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_tab_unselected.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_person.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_schedule.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/schedule_info_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/category_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_car.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_map_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_icon_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_primary_ios.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_circle_button_white.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_navigation_button.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_accept.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_route_walk.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/indicator_dot_active.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_outline.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/timemate_button_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_rating_background.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_image_placeholder.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_location_on.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ios_category_button_selector.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/pretendard_bold.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/pretendard_regular.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/font/pretendard_medium.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_manual_login.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_password_reset.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_signup_form.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_route_card.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_home_schedule.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_profile.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_improved.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_schedule_detail_ios.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_detail.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_add.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_notifications.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_add.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_route_option.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_account_switch.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/menu/bottom_nav_menu.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_route_options.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_today_schedule.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_improved.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_reminder_detail.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_recommendation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_recommendation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_directions_bottom_sheet.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggestion.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_place_with_image.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_ootd_recommendation.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_account.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_friend.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_friend_selection.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_card.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_schedule_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_friend_list.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_tomorrow_reminder_header.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_place_autocomplete.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/item_place_suggest.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/dialog_friend_selection.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_schedule_calendar.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:fade_in,0,F;fade_out,1,F;+animator:card_elevation_animator,2,F;+color:pastel_pink,3,V4001c03cb,2d001c03f4,;"#FFE4E6";card_selected,3,V400320777,31003207a4,;"#33B5DFFF";text_primary,3,V400370885,2e003708af,;"#1C1C1E";ios_green_light,3,V4006811ab,31006811d8,;"#E8F8F5";ios_red,3,V4001302cb,29001302f0,;"#FF3B30";text_input,3,V4003e0a44,2c003e0a6c,;"#000000";ios_purple,3,V4001402f6,2c0014031e,;"#AF52DE";ios_blue_light,3,V400671179,30006711a5,;"#B5DFFF";red,3,V4005e105a,25005e107b,;"#FF3B30";pastel_blue,3,V4001b039c,2d001b03c5,;"#E3F2FD";card_pressed,3,V4003307c8,30003307f4,;"#4DB5DFFF";white,3,V400040078,290004009d,;"#FFFFFFFF";ios_blue,3,V4000f0213,2a000f0239,;"#007AFF";dark_gray,3,V400260537,2b0026055e,;"#666666";warning,3,V400430b50,2900430b75,;"#FF9500";pastel_sky,3,V4002104bd,2c002104e5,;"#E8F6FD";purple_700,3,V4005c1003,2c005c102b,;"#8E44AD";shadow_light,3,V4006210c5,30006210f1,;"#1A000000";purple_500,3,V4005b0fd5,2c005b0ffd,;"#AF52DE";ios_gray6,3,V4006e12d1,2b006e12f8,;"#F2F2F7";ios_gray5,3,V4006d12a4,2b006d12cb,;"#E5E5EA";pastel_mint,3,V4001d03fa,2d001d0423,;"#E8F8F5";info,3,V400450be5,2600450c07,;"#007AFF";surface,3,V4002e06c2,29002e06e7,;"#FFFFFF";pastel_peach,3,V40020048d,2e002004b7,;"#FFEEE6";modal_handle,3,V400490c92,2e00490cbc,;"#C7C7CC";background_light,3,V400580f84,3200580fb2,;"#F8F9FA";card_background,3,V40031072d,310031075a,;"#FFFFFF";primary_color,3,V4000700ce,2f000700f9,;"#B5DFFF";background_secondary,3,V400560f15,3600560f47,;"#F2F2F7";bottom_nav_color,4,F;ios_pink,3,V400150324,2a0015034a,;"#FF2D92";route_text_secondary,3,V4004f0dc4,44004f0e04,;"@color/text_secondary";text_on_primary,3,V4003b09d5,31003b0a02,;"#FFFFFF";background,3,V4002d0678,2c002d06a0,;"#FFFFFF";success,3,V400420b06,2900420b2b,;"#34C759";ios_category_text_selector,5,F;route_card_background,3,V4004c0cf6,46004c0d38,;"@color/card_background";background_card,3,V4006f12fe,31006f132b,;"#FFFFFF";sky_blue_dark,3,V4000b0191,2f000b01bc,;"#007AFF";card_stroke,3,V400340819,2d00340842,;"#B5DFFF";primary,3,V4002b05d9,29002b05fe,;"#B5DFFF";route_accent,3,V400500e0a,3200500e38,;"@color/info";ios_orange_light,3,V4006a120f,32006a123d,;"#FFF8DC";background_tertiary,3,V400570f4d,3500570f7e,;"#FFFFFF";ios_green,3,V400110270,2b00110297,;"#34C759";error,3,V400440b9b,2700440bbe,;"#FF3B30";gray,3,V400280592,26002805b4,;"#999999";pastel_lavender,3,V4001f045a,31001f0487,;"#F0F0FF";divider,3,V4006310f7,290063111c,;"#E5E5EA";route_card_selected,3,V4004d0d3e,42004d0d7c,;"@color/card_selected";ios_orange,3,V40012029d,2c001202c5,;"#FF9500";green,3,V4005d1031,27005d1054,;"#34C759";ios_gray3,3,V4006c1277,2b006c129e,;"#C7C7CC";ios_blue_dark,3,V40010023f,2f0010026a,;"#0056CC";light_gray,3,V400270564,2c0027058c,;"#E0E0E0";route_warning,3,V400520e76,3600520ea8,;"@color/warning";black,3,V40003004d,2900030072,;"#FF000000";sky_blue_accent,3,V4000c01c2,31000c01ef,;"#5BB6D6";pastel_yellow,3,V4001e0429,2f001e0454,;"#FFF8DC";text_tertiary,3,V40039092d,2f00390958,;"#8E8E93";ios_purple_light,3,V4006b1243,32006b1271,;"#F0F0FF";accent,3,V4002c062a,28002c064e,;"#007AFF";modal_background,3,V400480c45,3200480c73,;"#F2F2F7";route_text_primary,3,V4004e0d82,40004e0dbe,;"@color/text_primary";orange,3,V4005f1081,28005f10a5,;"#FF9500";sky_blue_primary,3,V40009012b,3200090159,;"#B5DFFF";border_light,3,V400641122,2e0064114c,;"#F2F2F7";ios_red_light,3,V4006911de,2f00691209,;"#FFE4E6";text_input_hint,3,V4003f0a93,31003f0ac0,;"#8E8E93";ios_yellow,3,V400160350,2c00160378,;"#FFCC00";sky_blue_light,3,V4000a015f,30000a018b,;"#E8F6FD";background_primary,3,V400550edf,3400550f0f,;"#FFFFFF";text_hint,3,V4003a0981,2b003a09a8,;"#C7C7CC";route_success,3,V400510e3e,3600510e70,;"@color/success";sky_blue,3,V4000800ff,2a00080125,;"#B5DFFF";text_secondary,3,V4003808d8,3000380904,;"#3A3A3C";+dimen:card_elevation_pressed,6,V4000800ff,340008012f,;"6dp";route_item_spacing,6,V40028060f,310028063c,;"12dp";route_time_text_size,6,V400190405,3300190434,;"16sp";route_card_padding,6,V4001202c7,4000120303,;"@dimen/card_padding";route_detail_text_size,6,V4001803cf,3500180400,;"12sp";route_title_text_size,6,V400160362,3400160392,;"18sp";route_icon_size,6,V4001c0452,2e001c047c,;"24dp";card_margin,6,V400090134,2a0009015a,;"12dp";route_card_elevation,6,V400100243,4400100283,;"@dimen/card_elevation";modal_corner_radius,6,V4002104fc,320021052a,;"16dp";route_checkbox_size,6,V4001e04b1,32001e04df,;"20dp";route_button_height,6,V4002c068a,32002c06b8,;"48dp";card_corner_radius,6,V40005006a,3100050097,;"20dp";route_card_corner_radius,6,V4000f01f6,4c000f023e,;"@dimen/card_corner_radius";modal_handle_width,6,V40022052f,310022055c,;"36dp";route_button_corner_radius,6,V4002d06bd,39002d06f2,;"12dp";route_section_spacing,6,V4002705da,340027060a,;"16dp";route_content_spacing,6,V400290641,3300290670,;"8dp";modal_handle_height,6,V400230561,310023058e,;"4dp";route_icon_small,6,V4001d0481,2f001d04ac,;"20dp";card_padding,6,V4000a015f,2b000a0186,;"16dp";card_height,6,V4000c01b6,2a000c01dc,;"72dp";route_subtitle_text_size,6,V400170397,37001703ca,;"14sp";modal_handle_margin,6,V400240593,31002405c0,;"8dp";card_elevation,6,V40006009c,2c000600c4,;"2dp";card_elevation_selected,6,V4000700c9,35000700fa,;"4dp";route_card_spacing,6,V400130308,4000130344,;"@dimen/card_spacing";card_spacing,6,V4000b018b,2a000b01b1,;"8dp";route_card_margin,6,V400110288,3e001102c2,;"@dimen/card_margin";+drawable:button_primary,7,F;timemate_edittext_background,8,F;ic_walking,9,F;card_selected,10,F;timemate_button_primary,11,F;ic_check_circle,12,F;ic_check,13,F;ios_badge_background,14,F;route_normal_background,15,F;ic_directions_transit,16,F;ios_distance_background,17,F;status_badge_background,18,F;ic_arrow_back,19,F;route_recommended_background,20,F;ios_tab_selected,21,F;ic_friends,22,F;circle_dot,23,F;indicator_dot_inactive,24,F;button_secondary_ios,25,F;ic_schedule_notification,26,F;ic_time,27,F;ic_map_error,28,F;ic_image_error,29,F;card_completed,30,F;ios_card_background,31,F;bg_tag_rounded,32,F;recommended_badge,33,F;button_reject,34,F;ic_access_time,35,F;ic_transit,36,F;notification_badge_background,37,F;modal_handle,38,F;ic_directions_car,39,F;badge_background,40,F;ios_search_button,41,F;ic_home,42,F;ios_count_background,43,F;circle_background,44,F;ic_cancel,45,F;ic_add,46,F;ic_profile,47,F;ic_close,48,F;ic_directions,49,F;ic_launcher_background,50,F;ic_route_bus,51,F;timemate_button_outlined,52,F;ic_location_end,53,F;ic_calendar,54,F;ios_chip_background,55,F;category_tag_background,56,F;ic_driving,57,F;ic_snooze,58,F;card_overdue,59,F;circle_background_ios,60,F;ic_arrow_forward,61,F;ic_edit,62,F;weather_card_background,63,F;ios_header_blue_gradient,64,F;ic_location_start,65,F;ic_notifications,66,F;ic_location,67,F;ic_phone,68,F;bottom_sheet_background,69,F;card_normal,70,F;ic_weather,71,F;ios_image_placeholder,72,F;ic_arrow_right,73,F;ic_directions_walk,74,F;ios_tab_container,75,F;ios_tab_unselected,76,F;ic_person,77,F;ic_schedule,78,F;ios_button_background,79,F;schedule_info_background,80,F;category_background,81,F;ic_route_car,82,F;ic_map_placeholder,83,F;edit_text_background,84,F;ios_icon_background,85,F;button_primary_ios,86,F;ios_circle_button_white,87,F;ios_navigation_button,88,F;button_accept,89,F;ic_launcher_foreground,90,F;ic_route_walk,91,F;indicator_dot_active,92,F;button_outline,93,F;timemate_button_background,94,F;ios_rating_background,95,F;ic_image_placeholder,96,F;ic_location_on,97,F;ios_category_button_selector,98,F;+font:pretendard_bold,99,F;pretendard_regular,100,F;pretendard_medium,101,F;+id:editUserId,102,F;editUserId,103,F;editUserId,104,F;textRouteLabel,105,F;recyclerTodayScheduleBox,106,F;btnAddSchedule,107,F;scheduleIndicator,108,F;radioTimePriority,109,F;textScheduleTitle,110,F;textScheduleTitle,111,F;textScheduleTitle,108,F;textScheduleTitle,112,F;textSelectedFriends,113,F;btnTabPending,114,F;textNotificationTitle,115,F;editFriendId,116,F;textEmptySchedule,117,F;textRouteType,118,F;recyclerAccounts,119,F;nav_friends,120,F;checkboxWalking,121,F;cardBackground,122,F;cardBackground,123,F;cardBackground,124,F;textDrivingRecommended,121,F;textRecommended,118,F;btnGetDirections,113,F;btnLogout,109,F;textMemo,111,F;textMemo,122,F;textMemo,112,F;textMemo,125,F;textMemo,123,F;textMemo,124,F;checkCompleted,122,F;checkCompleted,123,F;textScheduleMemo,110,F;btnDismiss,126,F;textPlaceDistance,127,F;textResultCount,128,F;textNoScheduleToday,106,F;textBicycleRecommended,121,F;btnStartNavigation,126,F;cardFriends,111,F;textPlaceIcon,127,F;editTitle,113,F;recyclerRecommendations,128,F;layoutRouteInfo,113,F;recyclerRoutes,129,F;textCategory,130,F;textCategory,131,F;editFriendNickname,116,F;btnSignup,104,F;layoutIndicator,107,F;textOOTDTags,132,F;textCurrentUser,119,F;btnNextMonth,117,F;textUserId,109,F;textUserId,133,F;textUserId,134,F;textFriendName,135,F;textTemperature,106,F;rvDepSuggest,113,F;editPhone,104,F;textTollFare,126,F;btnPrevMonth,117,F;btnSnooze,126,F;editSearchLocation,128,F;btnSelectTime,113,F;textRoute,126,F;textRoute,136,F;btnSelectFriends,113,F;btnCategoryAttraction,128,F;textScheduleCount,137,F;textPublicRecommended,121,F;radioMale,104,F;textNickname,133,F;layoutHeader,138,F;btnAddAccount,119,F;textEmptyNotifications,139,F;btnViewDetail,136,F;textPlaceRating,127,F;textPublicTime,121,F;textNotificationCount,114,F;viewPagerSchedules,107,F;nav_schedule,120,F;layoutImageLoading,131,F;btnSaveSchedule,113,F;imgTransportMode,118,F;textTitle,126,F;textTitle,130,F;textTitle,122,F;textTitle,125,F;textTitle,123,F;textTitle,124,F;textTitle,136,F;checkboxFriend,135,F;textDuration,126,F;textDuration,105,F;textDuration,118,F;textDuration,136,F;textWeatherDescription,106,F;textDestination,111,F;textDestination,105,F;textDestination,112,F;textDestination,125,F;imgOOTD,132,F;textSectionTitle,137,F;textSectionTitle,140,F;layoutTransportDetails,105,F;textPlaceName,141,F;textPlaceName,142,F;textPlaceName,131,F;textPlaceName,127,F;textScheduleDate,111,F;textCost,105,F;textCost,118,F;editNewPassword,103,F;layoutActions,134,F;layoutActions,115,F;nav_home,120,F;textSelectedCount,143,F;btnEdit,111,F;btnEdit,122,F;btnEdit,123,F;btnQuickAddSchedule,106,F;textUserEmail,109,F;btnExpandMap,128,F;btnDeleteAccount,109,F;iconAccount,133,F;textEmail,133,F;textDrivingTime,121,F;editDeparture,113,F;layoutConfirmPassword,103,F;btnClose,129,F;btnClose,111,F;editEmail,104,F;bottomNavigationView,138,F;bottomNavigationView,106,F;bottomNavigationView,139,F;bottomNavigationView,109,F;bottomNavigationView,128,F;bottomNavigationView,144,F;bottomNavigationView,117,F;btnViewAll,107,F;btnCategoryRestaurant,128,F;btnCancel,102,F;btnCancel,103,F;btnCancel,113,F;btnCancel,104,F;btnCancel,129,F;btnCancel,143,F;btnCancel,121,F;btnLogin,102,F;btnViewAllSchedules,106,F;textTomorrowDeparture,106,F;cardLocationInfo,111,F;textDescription,118,F;textDrivingRoute,121,F;layoutMapLoading,128,F;textFeelsLike,106,F;editDestination,113,F;editNickname,104,F;textCurrentMonth,117,F;btnConfirm,103,F;btnConfirm,143,F;btnConfirm,121,F;btnConfirm,136,F;textPhone,131,F;recyclerTomorrowSchedule,106,F;fabAddSchedule,144,F;fabAddSchedule,117,F;textRouteSummary,118,F;cardMemo,111,F;layoutFriendList,138,F;btnSaveToSchedule,129,F;editMemo,113,F;textNotificationTime,115,F;btnBack,116,F;btnBack,139,F;btnBack,114,F;btnBack,109,F;btnBack,113,F;btnBack,126,F;textOotdDescription,106,F;textWalkingRecommended,121,F;textBicycleTime,121,F;layoutEmptyState,138,F;layoutEmptyState,114,F;layoutEmptyState,128,F;layoutEmptyState,144,F;layoutEmptyState,117,F;textStatus,111,F;textDepartureTime,126,F;textDepartureTime,136,F;recyclerViewFriends,143,F;layoutMapContainer,128,F;textRouteTitle,121,F;textWalkingTime,121,F;btnAccept,134,F;btnAccept,115,F;nav_recommendation,120,F;iconRouteType,105,F;editPassword,102,F;editPassword,104,F;textDateTime,126,F;textDateTime,122,F;textDateTime,125,F;textDateTime,123,F;textDateTime,136,F;recyclerNotifications,139,F;recyclerNotifications,114,F;recyclerOotd,106,F;textWalkingRoute,121,F;layoutFriends,112,F;btnSelectDate,113,F;imagePlace,131,F;btnTabAll,114,F;textCreatedDate,133,F;btnCurrentAccount,119,F;btnDirections,125,F;nav_profile,120,F;textLocation,122,F;textLocation,123,F;textLocation,124,F;textScheduleLocation,108,F;btnNavigation,127,F;textPlaceAddress,141,F;textPlaceAddress,142,F;textPlaceAddress,127,F;textPlaceCategory,141,F;textPlaceCategory,142,F;textPlaceCategory,127,F;textTransport,126,F;textTransport,136,F;btnEditSchedule,110,F;btnEditSchedule,112,F;textUserName,109,F;radioCostPriority,109,F;textTomorrowRoute,106,F;textSelectedDateTime,113,F;textRouteHeader,129,F;btnDeleteSchedule,110,F;btnDeleteSchedule,112,F;iconNotification,115,F;toolbar,144,F;checkboxDriving,121,F;textDialogDate,107,F;frameMapView,128,F;textName,134,F;recyclerSchedules,144,F;recyclerSchedules,117,F;textDistance,126,F;textDistance,131,F;textDistance,105,F;textDistance,118,F;layoutTodayScheduleBox,106,F;textUserInfo,103,F;rvDestSuggest,113,F;btnCloseDialog,107,F;btnCloseDialog,110,F;btnGoSignup,102,F;textAddress,130,F;textAddress,131,F;textFriends,111,F;textFriends,112,F;textFriends,125,F;checkboxPublicTransport,121,F;cardTomorrowReminder,106,F;cardRouteInfo,111,F;layoutWeather,106,F;textScheduleDestination,110,F;iconShared,125,F;textRouteCount,129,F;textFriendId,135,F;textTaxiRecommended,121,F;textTaxiRoute,121,F;textOOTDCategory,132,F;btnSearch,128,F;textTaxiTime,121,F;textCityName,106,F;textScheduleDeparture,110,F;textSelectedDateTitle,144,F;textTodayDate,106,F;recyclerFriends,138,F;textOOTDTitle,132,F;textHumidity,106,F;switchRealtimeData,109,F;textRouteInfo,113,F;textRouteInfo,111,F;textTomorrowTitle,106,F;textTime,124,F;layoutUserInfo,103,F;editConfirmPassword,103,F;btnCategoryCafe,128,F;layoutResultsContainer,128,F;btnAddFriend,116,F;textNotificationMessage,115,F;imagePlacePhoto,127,F;imgProfile,134,F;checkboxBicycle,121,F;textBicycleRoute,121,F;textFuelPrice,126,F;btnReject,134,F;btnReject,115,F;textOOTDDescription,132,F;checkboxRoute,105,F;btnDelete,111,F;textForgotPassword,102,F;textPublicRoute,121,F;layoutStatus,134,F;layoutNewPassword,103,F;recyclerTodaySchedule,106,F;layoutMemo,110,F;layoutMemo,112,F;textScheduleTime,111,F;textScheduleTime,108,F;textScheduleTime,112,F;fabAddFriend,138,F;textTomorrowDuration,106,F;btnCategoryAccommodation,128,F;btnNotifications,106,F;textScheduleDateTime,110,F;layoutLocation,112,F;radioFemale,104,F;btnSwitchAccount,109,F;textDeparture,111,F;textDeparture,105,F;textDeparture,112,F;textDeparture,125,F;calendarView,144,F;calendarView,117,F;checkboxTaxi,121,F;viewOnlineStatus,134,F;radioGenderGroup,104,F;radioGroupPriority,109,F;+layout:item_friend_selection,135,F;dialog_schedule_detail,107,F;item_place_suggest,142,F;item_tomorrow_reminder_header,140,F;item_place_suggestion,130,F;item_schedule_list,123,F;item_notification,115,F;activity_manual_login,102,F;item_schedule,122,F;dialog_schedule_detail_ios,111,F;dialog_route_options,121,F;activity_schedule_calendar,144,F;activity_account_switch,119,F;activity_main,145,F;activity_notification,139,F;dialog_schedule_detail_improved,110,F;activity_friend_add,116,F;item_route_option,118,F;activity_schedule_list,117,F;item_ootd_recommendation,132,F;item_recommendation,127,F;activity_notifications,114,F;item_place_with_image,131,F;item_friend,134,F;dialog_directions_bottom_sheet,129,F;activity_signup_form,104,F;item_route_card,105,F;activity_friend_list,138,F;item_today_schedule,124,F;item_tomorrow_reminder_card,136,F;item_schedule_improved,125,F;activity_password_reset,103,F;activity_schedule_reminder_detail,126,F;activity_profile,109,F;item_home_schedule,108,F;activity_recommendation,128,F;dialog_friend_selection,143,F;activity_schedule_add,113,F;item_schedule_header,137,F;activity_home,106,F;item_schedule_detail,112,F;item_place_autocomplete,141,F;item_account,133,F;+menu:bottom_nav_menu,120,F;+mipmap:ic_launcher_round,146,F;ic_launcher_round,147,F;ic_launcher_round,148,F;ic_launcher_round,149,F;ic_launcher_round,150,F;ic_launcher_round,151,F;ic_launcher,152,F;ic_launcher,153,F;ic_launcher,154,F;ic_launcher,155,F;ic_launcher,156,F;ic_launcher,157,F;+string:app_name,158,V400010010,2d00010039,;"TimeMate";+style:iOSFootnote,159,V4005e11f8,c00641357,;Nandroid\:textSize:13sp,android\:textColor:@color/text_tertiary,android\:fontFamily:@font/pretendard_regular,android\:letterSpacing:-0.01,android\:includeFontPadding:false,;TimeMateButtonPrimary,159,V4002507a6,c00320a98,;Nandroid\:textSize:17sp,android\:textStyle:normal,android\:fontFamily:@font/pretendard_bold,android\:paddingTop:14dp,android\:paddingBottom:14dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:background:@drawable/timemate_button_primary,android\:textColor:@color/white,android\:elevation:1dp,android\:letterSpacing:-0.02,android\:includeFontPadding:false,;TimeMateButtonOutlined,159,V4007d17b4,c00841956,;Nandroid\:textSize:16sp,android\:textStyle:normal,android\:paddingTop:14dp,android\:paddingBottom:14dp,android\:background:@drawable/timemate_button_outlined,android\:textColor:@color/sky_blue_accent,;TimeMateTextSubtitle,159,V400951b95,c009a1cad,;Nandroid\:textSize:18sp,android\:textStyle:normal,android\:textColor:@color/text_primary,android\:fontFamily:sans-serif,;iOSCallout,159,V400561091,c005c11f0,;Nandroid\:textSize:16sp,android\:textColor:@color/text_secondary,android\:fontFamily:@font/pretendard_regular,android\:letterSpacing:-0.01,android\:includeFontPadding:false,;iOSHeadline,159,V4003d0c2a,c00430d85,;Nandroid\:textSize:22sp,android\:textColor:@color/text_primary,android\:fontFamily:@font/pretendard_bold,android\:letterSpacing:-0.02,android\:includeFontPadding:false,;TimeMateBottomNavigation,159,V400bb21b0,c00be225f,;Nandroid\:background:@color/card_background,android\:elevation:8dp,;TimeMateTheme,159,V400040074,c00120447,;DTheme.Material3.DayNight.NoActionBar,colorPrimary:@color/sky_blue_primary,colorPrimaryVariant:@color/sky_blue_dark,colorOnPrimary:@color/text_primary,colorSecondary:@color/sky_blue_accent,colorSecondaryVariant:@color/sky_blue_dark,colorOnSecondary:@color/white,colorOnBackground:@color/text_primary,colorSurface:@color/card_background,colorOnSurface:@color/text_primary,android\:colorBackground:@color/background_secondary,android\:statusBarColor:@color/background_secondary,android\:windowLightStatusBar:true,android\:fontFamily:@font/pretendard_regular,;iOSTitle,159,V400350aca,c003b0c22,;Nandroid\:textSize:28sp,android\:textColor:@color/text_primary,android\:fontFamily:@font/pretendard_bold,android\:letterSpacing:-0.03,android\:includeFontPadding:false,;BottomSheetDialogTheme,159,V400de2703,c00e32881,;DTheme.Material3.DayNight.BottomSheetDialog,bottomSheetStyle:@style/BottomSheetStyle,android\:windowIsFloating:false,android\:statusBarColor:@android\:color/transparent,android\:windowSoftInputMode:adjustResize,;CalendarDateTextAppearance,159,V400d1250a,c00d525ed,;Nandroid\:textSize:16sp,android\:textStyle:normal,android\:textColor:@color/text_primary,;iOSBody,159,V4004d0ef5,c00541089,;Nandroid\:textSize:17sp,android\:textColor:@color/text_primary,android\:fontFamily:@font/pretendard_regular,android\:letterSpacing:-0.02,android\:includeFontPadding:false,android\:lineSpacingExtra:2dp,;TimeMateTextCaption,159,V400a31dd3,c00a81eeb,;Nandroid\:textSize:14sp,android\:textStyle:normal,android\:textColor:@color/text_tertiary,android\:fontFamily:sans-serif,;TimeMateAppBar,159,V400c12280,c00c823ff,;Nandroid\:background:@color/sky_blue_primary,android\:elevation:0dp,android\:paddingTop:16dp,android\:paddingBottom:16dp,android\:paddingStart:20dp,android\:paddingEnd:20dp,;Base.Theme.TimeMate,160,V400020064,c0005013d,;DTheme.Material3.DayNight.NoActionBar,;Base.Theme.TimeMate,161,V400020064,c0005013b,;DTheme.Material3.DayNight.NoActionBar,;TimeMateButton,159,V400150478,c0022076f,;Nandroid\:textSize:17sp,android\:textStyle:normal,android\:fontFamily:@font/pretendard_medium,android\:paddingTop:14dp,android\:paddingBottom:14dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:elevation:1dp,android\:background:@drawable/timemate_button_background,android\:textColor:@color/text_primary,android\:letterSpacing:-0.02,android\:includeFontPadding:false,;BottomSheetShapeAppearance,159,V400ed2a12,c00f32b87,;EcornerFamily:rounded,cornerSizeTopLeft:@dimen/modal_corner_radius,cornerSizeTopRight:@dimen/modal_corner_radius,cornerSizeBottomLeft:0dp,cornerSizeBottomRight:0dp,;TimeMateTextTemperature,159,V400ab1f0f,c00b02030,;Nandroid\:textSize:48sp,android\:textStyle:normal,android\:textColor:@color/text_primary,android\:fontFamily:sans-serif-light,;CalendarWeekDayTextAppearance,159,V400cb241c,c00cf2502,;Nandroid\:textSize:14sp,android\:textStyle:bold,android\:textColor:@color/text_secondary,;TimeMateTextTitle,159,V4008e1a7a,c00931b8d,;Nandroid\:textSize:24sp,android\:textStyle:bold,android\:textColor:@color/text_primary,android\:fontFamily:sans-serif,;iOSSubheadline,159,V400450d8d,c004b0eed,;Nandroid\:textSize:17sp,android\:textColor:@color/text_primary,android\:fontFamily:@font/pretendard_medium,android\:letterSpacing:-0.02,android\:includeFontPadding:false,;TimeMateCard,159,V400871977,c008b1a58,;Nandroid\:background:@drawable/ios_card_background,android\:layout_margin:8dp,android\:elevation:4dp,;Theme.TimeMate,160,V400070143,400007017f,;DBase.Theme.TimeMate,;TimeMateTextBody,159,V4009c1cb5,c00a11dcb,;Nandroid\:textSize:16sp,android\:textStyle:normal,android\:textColor:@color/text_secondary,android\:fontFamily:sans-serif,;TimeMateEditText,159,V400b32052,c00b8218f,;Nandroid\:background:@drawable/timemate_edittext_background,android\:textColorHint:@color/text_hint,android\:textColor:@color/text_primary,android\:padding:12dp,;CalendarHeaderTextAppearance,159,V400d725f5,c00db26d8,;Nandroid\:textSize:18sp,android\:textStyle:bold,android\:textColor:@color/text_primary,;iOSEditText,159,V4006f14ed,c007a178e,;Nandroid\:layout_height:50dp,android\:background:@drawable/timemate_edittext_background,android\:paddingStart:16dp,android\:paddingEnd:16dp,android\:textSize:17sp,android\:textColor:@color/text_input,android\:textColorHint:@color/text_input_hint,android\:fontFamily:@font/pretendard_regular,android\:letterSpacing:-0.02,android\:includeFontPadding:false,;BottomSheetStyle,159,V400e52889,c00eb2a0a,;DWidget.Material3.BottomSheet,shapeAppearanceOverlay:@style/BottomSheetShapeAppearance,behavior_peekHeight:400dp,behavior_hideable:true,behavior_skipCollapsed:false,android\:elevation:8dp,;iOSCaption,159,V40066135f,c006c14b9,;Nandroid\:textSize:12sp,android\:textColor:@color/text_tertiary,android\:fontFamily:@font/pretendard_regular,android\:letterSpacing:0,android\:includeFontPadding:false,;+xml:data_extraction_rules,162,F;backup_rules,163,F;