<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#B5DFFF">

    <!-- 중앙 컨텐츠 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:gravity="center">

        <!-- 앱 이름 (PNG 제거) -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="TimeMate"
            android:textSize="36sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:layout_marginBottom="16dp" />

        <!-- 앱 설명 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="당신의 시간을 더 스마트하게"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:alpha="0.8"
            android:layout_marginBottom="40dp" />

    </LinearLayout>

    <!-- 하단 로딩 인디케이터 -->
    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="80dp"
        android:indeterminateTint="@android:color/white" />

</RelativeLayout>
