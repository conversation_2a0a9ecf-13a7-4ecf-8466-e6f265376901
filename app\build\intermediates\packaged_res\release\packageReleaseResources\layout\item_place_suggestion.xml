<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <!-- 장소 아이콘 -->
    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/ic_location_on"
        app:tint="@color/primary_color" />

    <!-- 장소 정보 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 장소명 -->
        <TextView
            android:id="@+id/textTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="장소명"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 주소 -->
        <TextView
            android:id="@+id/textAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="주소"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 카테고리 -->
        <TextView
            android:id="@+id/textCategory"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="카테고리"
            android:textSize="12sp"
            android:textColor="@color/primary_color"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- 선택 화살표 -->
    <ImageView
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="8dp"
        android:src="@drawable/ic_arrow_forward"
        app:tint="@android:color/darker_gray" />

</LinearLayout>
